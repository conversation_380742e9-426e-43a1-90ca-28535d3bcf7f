"""
Services for Chat Commands Plugin
"""

import json
import os
import logging
import requests
import uuid
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import traceback

# Use absolute imports for testing compatibility
try:
    from .models import ChatCommand, WebhookConfig, WebhookMessage, CommandResponse, DebugConfig, CommandConfig
except ImportError:
    from models import ChatCommand, WebhookConfig, WebhookMessage, CommandResponse, DebugConfig, CommandConfig

logger = logging.getLogger(__name__)


class ChatCommandService:
    """Service for managing chat commands"""

    def __init__(self, plugin_dir: str, app=None):
        self.plugin_dir = plugin_dir
        self.app = app  # Flask app instance for plugin communication

        # Use configs directory for persistent storage (Docker mount compatible)
        self.configs_dir = os.path.join(os.path.dirname(os.path.dirname(plugin_dir)), 'configs', 'plugins', 'chat_commands')
        os.makedirs(self.configs_dir, exist_ok=True)

        # Config files in persistent directory
        self.commands_file = os.path.join(self.configs_dir, 'commands.json')
        self.config_file = os.path.join(self.configs_dir, 'config.json')

        # Legacy files in plugin directory (for migration)
        self.legacy_commands_file = os.path.join(plugin_dir, 'commands.json')
        self.legacy_config_file = os.path.join(plugin_dir, 'config.json')

        self._commands: Dict[str, ChatCommand] = {}
        self._webhook_config: WebhookConfig = WebhookConfig()
        self._debug_config: DebugConfig = DebugConfig()
        self._command_config: CommandConfig = CommandConfig()

        # Migrate legacy files if they exist
        self._migrate_legacy_files()

        self.load_commands()
        self.load_config()

    def _migrate_legacy_files(self):
        """Migrate legacy config files from plugin directory to configs directory"""
        try:
            # Migrate commands.json
            if os.path.exists(self.legacy_commands_file) and not os.path.exists(self.commands_file):
                logger.info(f"Migrating commands from {self.legacy_commands_file} to {self.commands_file}")
                import shutil
                shutil.copy2(self.legacy_commands_file, self.commands_file)
                logger.info("Commands migration completed")

            # Migrate config.json
            if os.path.exists(self.legacy_config_file) and not os.path.exists(self.config_file):
                logger.info(f"Migrating config from {self.legacy_config_file} to {self.config_file}")
                import shutil
                shutil.copy2(self.legacy_config_file, self.config_file)
                logger.info("Config migration completed")

        except Exception as e:
            logger.error(f"Error during legacy file migration: {e}")

    def load_commands(self):
        """Load commands from JSON file"""
        try:
            if os.path.exists(self.commands_file):
                with open(self.commands_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._commands = {
                        cmd: ChatCommand.from_dict(cmd_data) 
                        for cmd, cmd_data in data.items()
                    }
                # Note: This will be checked after _debug_config is loaded
                logger.info(f"Loaded {len(self._commands)} chat commands")
            else:
                # Create default commands
                self._create_default_commands()
                self.save_commands()
        except Exception as e:
            logger.error(f"Error loading commands: {e}")
            self._create_default_commands()
    
    def load_config(self):
        """Load plugin configuration"""
        try:
            # First load from local config file
            local_config = {}
            if os.path.exists(self.config_file):
                try:
                    # Try UTF-8 first
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        local_config = json.load(f)
                        logger.info(f"ChatCommandService: Raw data loaded from {self.config_file}: {json.dumps(local_config)}")
                except (UnicodeDecodeError, json.JSONDecodeError) as e:
                    if "UTF-8 BOM" in str(e):
                        # Try UTF-8 with BOM signature
                        try:
                            with open(self.config_file, 'r', encoding='utf-8-sig') as f:
                                local_config = json.load(f)
                                logger.info("Successfully loaded config with UTF-8-sig encoding")
                                # Recreate the file without BOM
                                self._recreate_config_file(local_config)
                        except Exception as e2:
                            logger.error(f"Failed to load config with UTF-8-sig: {e2}")
                            # Delete corrupted file and use defaults
                            os.remove(self.config_file)
                            logger.info("Deleted corrupted config file, will use defaults")
                    else:
                        # If UTF-8 fails, try UTF-16 and recreate the file
                        logger.warning(f"Config file {self.config_file} has encoding issues, attempting to fix...")
                        try:
                            with open(self.config_file, 'r', encoding='utf-16') as f:
                                content = f.read()
                                # Remove extra spaces between characters (UTF-16 artifact)
                                content = content.replace(' ', '')
                                local_config = json.loads(content)
                                logger.info("Successfully recovered config from UTF-16 encoding")
                                # Recreate the file with proper UTF-8 encoding
                                self._recreate_config_file(local_config)
                        except Exception as e:
                            logger.error(f"Failed to recover config file: {e}")
                            # Delete corrupted file and use defaults
                            os.remove(self.config_file)
                            logger.info("Deleted corrupted config file, will use defaults")

            # Try to get main plugin config from plugin manager if available
            main_config = {}
            if self.app and hasattr(self.app, 'plugin_manager'):
                plugin_configs = getattr(self.app.plugin_manager, 'plugin_configs', {})
                main_config = plugin_configs.get('chat_commands', {})
                logger.info(f"ChatCommandService: Main config from plugin_manager: {json.dumps(main_config)}")

            # Merge configurations (main config takes precedence over local config)
            webhook_data = {**local_config.get('webhook_config', {}), **main_config.get('webhook_config', {})}
            self._webhook_config = WebhookConfig.from_dict(webhook_data)
            logger.info(f"ChatCommandService: Loaded webhook_config: {self._webhook_config.to_dict()}")

            debug_data = {**local_config.get('debug_config', {}), **main_config.get('debug_config', {})}
            self._debug_config = DebugConfig.from_dict(debug_data)
            logger.info(f"ChatCommandService: Loaded debug_config: {self._debug_config.to_dict()}")

            command_data = {**local_config.get('command_config', {}), **main_config.get('command_config', {})}
            self._command_config = CommandConfig.from_dict(command_data)
            logger.info(f"ChatCommandService: Loaded command_config: {self._command_config.to_dict()}")

            logger.info("Loaded plugin configuration")

            # Save merged config to local file if it doesn't exist or if main config has updates
            if not os.path.exists(self.config_file) or main_config:
                self.save_config()

        except Exception as e:
            logger.error(f"Error loading config: {e}")
            logger.error(f"ChatCommandService: _webhook_config state on error: {getattr(self, '_webhook_config', 'Not initialized')}")
            logger.error(f"Traceback: {traceback.format_exc()}")

    def _recreate_config_file(self, config_data):
        """Recreate config file with proper UTF-8 encoding"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Successfully recreated config file with UTF-8 encoding")
        except Exception as e:
            logger.error(f"Failed to recreate config file: {e}")

    def save_commands(self):
        """Save commands to JSON file"""
        try:
            data = {cmd: command.to_dict() for cmd, command in self._commands.items()}
            with open(self.commands_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info("Saved chat commands")
        except Exception as e:
            logger.error(f"Error saving commands: {e}")

    def reload_commands(self):
        """Reload commands from JSON file"""
        try:
            old_count = len(self._commands)
            self.load_commands()
            new_count = len(self._commands)
            logger.info(f"Reloaded commands: {old_count} -> {new_count}")
            return True
        except Exception as e:
            logger.error(f"Error reloading commands: {e}")
            return False
    
    def save_config(self):
        """Save plugin configuration"""
        try:
            data = {
                'webhook_config': self._webhook_config.to_dict(),
                'debug_config': self._debug_config.to_dict(),
                'command_config': self._command_config.to_dict(),
                'webhook_enabled': True,
                'auto_response_enabled': True
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info("Saved plugin configuration")
        except Exception as e:
            logger.error(f"Error saving config: {e}")
    
    def _create_default_commands(self):
        """Create default commands based on Stools-MTYB"""
        default_commands = {
            "android_help": ChatCommand(
                command="android_help",
                description="Provide Android help instructions",
                response_text="🤖 𝑨𝒏𝒅𝒓𝒐𝒊𝒅 𝑯𝒆𝒍𝒑 🤖\n\n1. Download ArmodVPN From Playstore.\n2. Copy config\n3. Buka ArmodVPN dan Click Import Clipboard\n4. Connect\n\nQA:\nIf bypass not working sometimes, try on/off airplane mode.\n\n🤖 𝑨𝒏𝒅𝒓𝒐𝒊𝒅 𝑯𝒆𝒍𝒑 🤖",
                image_urls=["https://down-my.img.susercontent.com/file/my-11134207-7r98r-lrm0x5m2z94n40"]
            ),
            "ios_help": ChatCommand(
                command="ios_help",
                description="Provide iOS help instructions",
                response_text="🤖 𝑰𝑶𝑺 𝑯𝒆𝒍𝒑 🤖\n\n1. Download [V2Box] Dari AppStore\n2. Buka [V2Box] dan Pergi [Config]\n3. Copy [Config] Kamu\n4. Klik ➕ [Import v2ray uri from clipboard]\n5. Klik Config itu dalam [V2Box]\n6. Pergi [Home] Klik [Connect] ▶️\n\nQA:\nIf bypass not working sometimes, try on/off airplane mode.\n\n🤖 𝑰𝑶𝑺 𝑯𝒆𝒍𝒑 🤖",
                image_urls=["https://down-my.img.susercontent.com/file/my-11134207-7r98s-lrobk5lv3v1z67"]
            ),
            "android_digi": ChatCommand(
                command="android_digi",
                description="Provide Android Digi help instructions",
                response_text="🤖 𝑨𝒏𝒅𝒓𝒐𝒊𝒅 𝑫𝒊𝒈𝒊 𝑯𝒆𝒍𝒑 🤖\n\n✨Digi Tanpa Langganan Setting\n\n1. Buka APN (Access Point Names) yang baru kat Android Sim Setting\n2. [Nama] APN letak apa2 pun boleh\n3. Edit yang macam image saya bagi\n4. Yang lain tak perlu edit, terus tekan save \n5. Tukar/Guna pergi yang APN kamu buka itu\n6. Connect VPN\n\n🤖 𝑨𝒏𝒅𝒓𝒐𝒊𝒅 𝑫𝒊𝒈𝒊 𝑯𝒆𝒍𝒑 🤖",
                image_urls=[
                    "https://down-my.img.susercontent.com/file/my-11134207-7r98z-lrrlyk9ukjs440",
                    "https://down-my.img.susercontent.com/file/my-11134207-7r98p-lrrlyk9uj57o53"
                ]
            ),
            "ios_digi": ChatCommand(
                command="ios_digi",
                description="Provide iOS Digi help instructions",
                response_text="🤖 𝒊𝑶𝑺 𝑫𝒊𝒈𝒊 𝑯𝒆𝒍𝒑 🤖\n\n⚠️ Digi-Tanpa Langganan IOS Guide\n🔗 https://ogne.tech/apns/generator/\n\n1. Open the link in Safari\n2. Enter \"hos\"\n3. Tekan \"Install\"\n4. Pergi iPhone Setting install APN itu\n5. Lepas Install APN siap, connect VPN (akan send you config & tutorial)\n\n🤖 𝒊𝑶𝑺 𝑫𝒊𝒈𝒊 𝑯𝒆𝒍𝒑 🤖",
                image_urls=["https://down-my.img.susercontent.com/file/my-11134207-7r98q-lrrlyk9uhqn852"]
            ),
            "help": ChatCommand(
                command="help",
                description="Provide command list",
                response_text="🎖️ᴄᴏᴍᴍᴀɴᴅ ʟɪꜱᴛ🎖️\n----------------------------------\n\n#𝒃𝒚𝒑𝒂𝒔𝒔 >> Dapat semua config yang kita ade jual.\n#𝒗𝒑𝒏 >> Explain apa vpn itu.\n#𝒂𝒏𝒅𝒓𝒐𝒊𝒅_𝒉𝒆𝒍𝒑 >> Dapat android connect VPN tutorial\n#𝒊𝒐𝒔_𝒉𝒆𝒍𝒑 >> Dapat iOS connect VPN tutorial\n#𝒂𝒏𝒅𝒓𝒐𝒊𝒅_𝒅𝒊𝒈𝒊 >> Dapat Digi APN Setting for android\n#𝒊𝒐𝒔_𝒅𝒊𝒈𝒊 >> Dapat Digi APN setting for iOS\n\n⚠️ Note: VPN configuration is now managed through the VPN Config Generator plugin."
            )
        }
        self._commands = default_commands
    
    def get_all_commands(self) -> Dict[str, Dict[str, Any]]:
        """Get all commands as dictionaries, safe for serialization"""
        # Return a dictionary of command data, ensuring handler_callback is removed
        return {name: cmd.to_dict() for name, cmd in self._commands.items()}
    
    def get_command(self, command: str) -> Optional[ChatCommand]:
        """Get a specific command"""
        return self._commands.get(command)
    
    def add_command(self, command: ChatCommand) -> bool:
        """Add a new command"""
        try:
            command.updated_at = datetime.now().isoformat()
            self._commands[command.command] = command
            self.save_commands()
            return True
        except Exception as e:
            logger.error(f"Error adding command: {e}")
            return False

    def register_external_command(self, command: ChatCommand, plugin_name: str, handler_callback=None, locked: bool = False) -> bool:
        """Register a command from an external plugin.
        If locked=True, the command becomes non-editable/deletable via UI/API.
        """
        try:
            # Mark command as external
            command.plugin_source = plugin_name
            command.handler_callback = handler_callback
            command.locked = bool(getattr(command, 'locked', False) or locked)
            command.updated_at = datetime.now().isoformat()

            self._commands[command.command] = command
            logger.info(f"Registered external command '{command.command}' from plugin '{plugin_name}' (locked={command.locked})")
            return True
        except Exception as e:
            logger.error(f"Error registering external command: {e}")
            return False

    def unregister_external_command(self, command_name: str, plugin_name: str) -> bool:
        """Unregister a command from an external plugin"""
        try:
            if command_name in self._commands:
                command = self._commands[command_name]
                if hasattr(command, 'plugin_source') and command.plugin_source == plugin_name:
                    del self._commands[command_name]
                    logger.info(f"Unregistered external command '{command_name}' from plugin '{plugin_name}'")
                    return True
                else:
                    logger.warning(f"Command '{command_name}' is not owned by plugin '{plugin_name}'")
                    return False
            else:
                logger.warning(f"Command '{command_name}' not found")
                return False
        except Exception as e:
            logger.error(f"Error unregistering external command: {e}")
            return False
    
    def update_command(self, command_name: str, command: ChatCommand) -> bool:
        """Update an existing command. Prevent updates to locked commands."""
        try:
            if command_name in self._commands:
                existing = self._commands[command_name]
                if getattr(existing, 'locked', False):
                    logger.warning(f"Attempt to update locked command '{command_name}' blocked")
                    return False
                command.locked = getattr(existing, 'locked', False)
                command.plugin_source = existing.plugin_source
                command.handler_callback = existing.handler_callback
                command.updated_at = datetime.now().isoformat()
                self._commands[command_name] = command
                self.save_commands()
                return True
            return False
        except Exception as e:
            logger.error(f"Error updating command: {e}")
            return False

    def delete_command(self, command_name: str) -> bool:
        """Delete a command. Prevent deletion of locked commands."""
        try:
            if command_name in self._commands:
                existing = self._commands[command_name]
                if getattr(existing, 'locked', False):
                    logger.warning(f"Attempt to delete locked command '{command_name}' blocked")
                    return False
                del self._commands[command_name]
                self.save_commands()
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting command: {e}")
            return False



    def get_webhook_config(self) -> WebhookConfig:
        """Get webhook configuration"""
        return self._webhook_config

    def update_webhook_config(self, config: WebhookConfig) -> bool:
        """Update webhook configuration"""
        try:
            self._webhook_config = config
            self.save_config()
            return True
        except Exception as e:
            logger.error(f"Error updating webhook config: {e}")
            return False

    def get_debug_config(self) -> DebugConfig:
        """Get debug configuration"""
        return self._debug_config

    def update_debug_config(self, config: DebugConfig) -> bool:
        """Update debug configuration"""
        try:
            self._debug_config = config
            self.save_config()
            return True
        except Exception as e:
            logger.error(f"Error updating debug config: {e}")
            return False

    def get_command_config(self) -> CommandConfig:
        """Get command configuration"""
        return self._command_config

    def get_command_prefix(self) -> str:
        """Get the current command prefix"""
        return self._command_config.command_prefix

    def update_command_config(self, config: CommandConfig) -> bool:
        """Update command configuration"""
        try:
            self._command_config = config
            self.save_config()
            return True
        except Exception as e:
            logger.error(f"Error updating command config: {e}")
            return False

    def is_debug_enabled(self, debug_type: str = None) -> bool:
        """Check if debug logging is enabled for a specific type"""
        if not self._debug_config.enabled:
            return False

        if debug_type is None:
            return True

        debug_flags = {
            'webhook_data': self._debug_config.log_webhook_data,
            'message_parsing': self._debug_config.log_message_parsing,
            'command_processing': self._debug_config.log_command_processing,
            'response_generation': self._debug_config.log_response_generation,
            'info_messages': self._debug_config.log_info_messages,
            'access_audit': self._debug_config.log_access_audit,
            'werkzeug': self._debug_config.log_werkzeug,
            'service_operations': self._debug_config.log_service_operations
        }

        return debug_flags.get(debug_type, False)


class MessageProcessor:
    """Processes incoming webhook messages and generates responses"""

    def __init__(self, command_service: ChatCommandService, shopee_api_client=None):
        self.command_service = command_service
        self.shopee_api_client = shopee_api_client

    def process_webhook_message(self, webhook_data: Dict[str, Any]) -> Optional[List[CommandResponse]]:
        """Process incoming webhook message and return responses if needed"""
        try:
            # Parse webhook message
            message = WebhookMessage.from_webhook_data(webhook_data)
            
            # Use debug config to control logging
            if self.command_service.is_debug_enabled('command_processing'):
                logger.info(f"Processed webhook message - Content: '{message.content}', Type: {message.message_type}, Sender: {message.sender_name}")

            # Check if message is from ourselves (don't respond to our own messages)
            webhook_message = webhook_data.get('message', {})
            send_by_yourself = webhook_message.get('send_by_yourself', False)
            if send_by_yourself:
                if self.command_service.is_debug_enabled('command_processing'):
                    logger.debug("Ignoring message sent by ourselves")
                return None

            # Skip status updates and empty content
            if message.message_type == 'status_update' or not message.content:
                if self.command_service.is_debug_enabled('command_processing'):
                    logger.debug(f"Ignoring {message.message_type} or empty content")
                return None

            # Get command prefix from configuration
            command_config = self.command_service.get_command_config()
            prefix = command_config.command_prefix

            # Check if message starts with configured prefix
            if not message.content.startswith(prefix):
                if self.command_service.is_debug_enabled('command_processing'):
                    logger.debug(f"Message doesn't start with '{prefix}': '{message.content}'")
                return None

            # Extract command and parameters
            content = message.content.replace('\n', ' ')
            prefix_len = len(prefix)
            parts = content[prefix_len:].split(' ')
            command_name = parts[0] if parts else ''
            params = parts[1:] if len(parts) > 1 else []

            # Handle case sensitivity
            if not command_config.case_sensitive:
                command_name = command_name.lower()

            if self.command_service.is_debug_enabled('command_processing'):
                logger.info(f"Processing command: {command_name} with params: {params}")

            # Get command configuration
            command = self.command_service.get_command(command_name)
            if not command or not command.enabled:
                if self.command_service.is_debug_enabled('command_processing'):
                    logger.info(f"Command {command_name} not found or disabled")
                return None

            # Handle external commands with callbacks
            if hasattr(command, 'handler_callback') and command.handler_callback:
                try:
                    # Call external handler
                    external_responses = command.handler_callback(message, params)
                    if external_responses:
                        return external_responses
                except Exception as e:
                    logger.error(f"Error calling external command handler: {e}")
                    return [CommandResponse(text="Error processing command. Please try again later.")]

            # Handle standard commands
            return self._handle_standard_command(command)

        except Exception as e:
            logger.error(f"Error processing webhook message: {e}")
            return None

    def _handle_standard_command(self, command: ChatCommand) -> List[CommandResponse]:
        """Handle standard commands with text and images"""
        responses = []

        # Check if this command has specific image-first setting, otherwise use global config
        command_config = self.command_service.get_command_config()
        send_images_first = getattr(command, 'send_images_first', False) or command_config.send_images_first

        # Split text into multiple messages if it exceeds the limit
        text_responses = []
        if command.response_text:
            text_responses = self._split_message_if_needed(command.response_text, command_config.message_split_limit)

        # Add mark_as_unread flag to responses if needed
        mark_as_unread = getattr(command, 'mark_as_unread', False)

        if send_images_first:
            # Send images first, then text
            for image_url in command.image_urls:
                responses.append(CommandResponse(image_urls=[image_url], mark_as_unread=mark_as_unread))

            for text in text_responses:
                responses.append(CommandResponse(text=text, mark_as_unread=mark_as_unread))
        else:
            # Send text first, then images (default behavior)
            for text in text_responses:
                responses.append(CommandResponse(text=text, mark_as_unread=mark_as_unread))

            for image_url in command.image_urls:
                responses.append(CommandResponse(image_urls=[image_url], mark_as_unread=mark_as_unread))

        return responses

    def _split_message_if_needed(self, text: str, limit: int) -> List[str]:
        """Split message into multiple parts if it exceeds the character limit"""
        if len(text) <= limit:
            return [text]

        messages = []
        current_message = ""

        # Split by sentences first to avoid breaking mid-sentence
        sentences = text.split('. ')

        for i, sentence in enumerate(sentences):
            # Add period back except for the last sentence
            if i < len(sentences) - 1:
                sentence += '. '

            # Check if adding this sentence would exceed the limit
            if len(current_message + sentence) > limit:
                if current_message:
                    messages.append(current_message.strip())
                    current_message = sentence
                else:
                    # Single sentence is too long, split by words
                    words = sentence.split(' ')
                    for word in words:
                        if len(current_message + ' ' + word) > limit:
                            if current_message:
                                messages.append(current_message.strip())
                                current_message = word
                            else:
                                # Single word is too long, split by characters
                                if len(word) > limit:
                                    for j in range(0, len(word), limit):
                                        messages.append(word[j:j+limit])
                                else:
                                    current_message = word
                        else:
                            current_message += ' ' + word if current_message else word
            else:
                current_message += sentence

        if current_message:
            messages.append(current_message.strip())

        return messages





    def _generate_random_number(self) -> str:
        """Generate random number for username"""
        import random
        return str(random.randint(10000, 99999))


class WebhookManager:
    """Manages webhook registration and configuration with ShopeeAPI"""

    def __init__(self, command_service: ChatCommandService):
        self.command_service = command_service

    def test_shopee_api_connectivity(self) -> Dict[str, Any]:
        """Test connectivity to ShopeeAPI"""
        webhook_config = self.command_service.get_webhook_config()

        try:
            # Use root endpoint instead of /health since ShopeeAPI doesn't have /health
            response = requests.get(
                f"{webhook_config.shopee_api_base_url}/",
                timeout=webhook_config.timeout
            )

            if response.status_code == 200:
                try:
                    response_data = response.json()
                    # Check if this is the expected ShopeeAPI response
                    if response_data.get('message') == 'Shopee API Service':
                        return {
                            'status': 'success',
                            'message': 'ShopeeAPI is accessible and responding correctly',
                            'url': webhook_config.shopee_api_base_url,
                            'response_time': response.elapsed.total_seconds(),
                            'service_info': response_data
                        }
                    else:
                        return {
                            'status': 'warning',
                            'message': 'ShopeeAPI is accessible but returned unexpected response',
                            'url': webhook_config.shopee_api_base_url,
                            'response_time': response.elapsed.total_seconds(),
                            'response_data': response_data
                        }
                except ValueError:
                    # Not JSON response
                    return {
                        'status': 'warning',
                        'message': 'ShopeeAPI is accessible but returned non-JSON response',
                        'url': webhook_config.shopee_api_base_url,
                        'response_time': response.elapsed.total_seconds(),
                        'response_text': response.text[:200]
                    }
            else:
                return {
                    'status': 'error',
                    'message': f'ShopeeAPI returned status {response.status_code}',
                    'url': webhook_config.shopee_api_base_url
                }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Failed to connect to ShopeeAPI: {str(e)}',
                'url': webhook_config.shopee_api_base_url
            }

    def register_webhook(self) -> Dict[str, Any]:
        """Register webhook with ShopeeAPI"""
        webhook_config = self.command_service.get_webhook_config()

        if not webhook_config.enabled:
            return {
                'status': 'error',
                'message': 'Webhook is disabled in configuration'
            }

        try:
            webhook_data = {
                'url': webhook_config.get_full_webhook_url(),
                'events': ['message_received'],
                'enabled': True,
                'retry_count': webhook_config.retry_count,
                'retry_delay': webhook_config.retry_delay
            }

            response = requests.post(
                webhook_config.get_shopee_webhook_register_url(),
                json=webhook_data,
                timeout=webhook_config.timeout
            )

            if response.status_code == 200:
                return {
                    'status': 'success',
                    'message': 'Webhook registered successfully',
                    'webhook_url': webhook_config.get_full_webhook_url()
                }
            else:
                return {
                    'status': 'error',
                    'message': f'Failed to register webhook: HTTP {response.status_code}',
                    'details': response.text
                }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'Error registering webhook: {str(e)}'
            }

    def unregister_webhook(self) -> Dict[str, Any]:
        """Unregister webhook from ShopeeAPI"""
        webhook_config = self.command_service.get_webhook_config()

        try:
            webhook_data = {
                'url': webhook_config.get_full_webhook_url()
            }

            response = requests.delete(
                f"{webhook_config.shopee_api_base_url}/api/webhooks/unregister",
                json=webhook_data,
                timeout=webhook_config.timeout
            )

            if response.status_code == 200:
                return {
                    'status': 'success',
                    'message': 'Webhook unregistered successfully'
                }
            else:
                return {
                    'status': 'error',
                    'message': f'Failed to unregister webhook: HTTP {response.status_code}',
                    'details': response.text
                }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'Error unregistering webhook: {str(e)}'
            }

    def test_webhook_endpoint(self) -> Dict[str, Any]:
        """Test the webhook endpoint accessibility"""
        webhook_config = self.command_service.get_webhook_config()

        try:
            # Send a test webhook payload
            test_payload = {
                'event': 'test',
                'message': {
                    'message_id': 'test-123',
                    'content': {'text': '#help'},
                    'from_name': 'test-user',
                    'from_id': 'test-user-id',
                    'send_by_yourself': False
                },
                'conversation_id': 'test-conversation'
            }

            response = requests.post(
                webhook_config.get_full_webhook_url(),
                json=test_payload,
                timeout=webhook_config.timeout
            )

            if response.status_code == 200:
                return {
                    'status': 'success',
                    'message': 'Webhook endpoint is accessible and responding',
                    'url': webhook_config.get_full_webhook_url()
                }
            else:
                return {
                    'status': 'error',
                    'message': f'Webhook endpoint returned status {response.status_code}',
                    'url': webhook_config.get_full_webhook_url()
                }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'Failed to reach webhook endpoint: {str(e)}',
                'url': webhook_config.get_full_webhook_url()
            }

    def get_webhook_status(self) -> Dict[str, Any]:
        """Get comprehensive webhook status"""
        webhook_config = self.command_service.get_webhook_config()

        status = {
            'webhook_config': webhook_config.to_dict(),
            'shopee_api_connectivity': self.test_shopee_api_connectivity(),
            'webhook_endpoint_test': self.test_webhook_endpoint()
        }

        # Determine overall status
        if (status['shopee_api_connectivity']['status'] == 'success' and
            status['webhook_endpoint_test']['status'] == 'success' and
            webhook_config.enabled):
            status['overall_status'] = 'healthy'
        else:
            status['overall_status'] = 'issues_detected'

        return status
