"""
VPN Plugin Implementation
Handles VPN server management, client configuration using blueblue.api.limjianhui.com API
"""

import logging
from typing import Dict, Any, Optional
from flask import Blueprint
from core.plugin_manager import PluginInterface
from .services.vpn_api_service import VPNAPIService
from .services.vpn_config_service import VPNConfigService
from .services.vpn_redemption_service import VPNRedemptionService
from .routes.vpn_routes import create_vpn_blueprint

logger = logging.getLogger(__name__)

class Plugin(PluginInterface):
    """VPN Plugin Main Class"""
    
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.name = "vpn"
        self.version = "2.0.0"  # Updated version for complete implementation
        self.description = "Complete VPN management for servers, clients, configurations, and health monitoring"
        self.dependencies = []
        self.url_prefix = "/admin/vpn"  # Custom URL prefix for admin routes

        # Services
        self.api_service = None
        self.config_service = None
        self.redemption_service = None
        self.blueprint = None

    def initialize(self) -> bool:
        """Initialize the VPN plugin"""
        try:
            logger.info("Initializing VPN plugin...")

            # Initialize services
            self.api_service = VPNAPIService(self.config)
            self.config_service = VPNConfigService(self.config)
            self.redemption_service = VPNRedemptionService(self.config)

            # Create blueprint with services
            self.blueprint = create_vpn_blueprint(
                self,
                self.api_service,
                self.config_service,
                self.redemption_service
            )
            
            # Note: API authentication will be done on first request to avoid app context issues
            logger.info("VPN API service initialized - authentication will be done on first request")
            
            logger.info("VPN plugin initialized successfully")
            logger.info(f"VPN API Base URL: {self.api_service.base_url}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize VPN plugin: {e}")
            return False
            
    def shutdown(self) -> bool:
        """Shutdown the VPN plugin"""
        try:
            logger.info("Shutting down VPN plugin...")
            
            # Close API session if exists
            if self.api_service and hasattr(self.api_service, 'session'):
                self.api_service.session.close()
                
            logger.info("VPN plugin shutdown successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error shutting down VPN plugin: {e}")
            return False
            
    def get_blueprint(self) -> Optional[Blueprint]:
        """Return the Flask blueprint for VPN routes"""
        return self.blueprint
        
    def get_config_schema(self) -> Dict[str, Any]:
        """Return the configuration schema for VPN plugin"""
        return {
            "enabled": {
                "type": "boolean",
                "default": True,
                "description": "Enable/disable VPN plugin"
            },
            "client_config": {
                "type": "object",
                "properties": {
                    "default_traffic_gb": {
                        "type": "integer",
                        "default": 100,
                        "description": "Default traffic limit in GB for new clients"
                    },
                    "default_expiry_days": {
                        "type": "integer",
                        "default": 30,
                        "description": "Default expiry period in days for new clients"
                    },
                    "auto_remove_expired": {
                        "type": "boolean",
                        "default": False,
                        "description": "Automatically remove expired clients"
                    },
                    "expiry_warning_days": {
                        "type": "integer",
                        "default": 7,
                        "description": "Days before expiry to show warning"
                    }
                }
            },
            "server_config": {
                "type": "object",
                "properties": {
                    "default_xray_path": {
                        "type": "string",
                        "default": "/etc/xray/config.json",
                        "description": "Default Xray configuration path"
                    },
                    "default_xray_service": {
                        "type": "string",
                        "default": "xray",
                        "description": "Default Xray service name"
                    },
                    "ssh_timeout": {
                        "type": "integer",
                        "default": 30,
                        "description": "SSH connection timeout in seconds"
                    },
                    "health_check_interval": {
                        "type": "integer",
                        "default": 300,
                        "description": "Health check interval in seconds"
                    }
                }
            },
            "config_templates": {
                "type": "object",
                "properties": {
                    "templates_file": {
                        "type": "string",
                        "default": "configs/services/config_templates.json",
                        "description": "Path to configuration templates file"
                    },
                    "backup_directory": {
                        "type": "string",
                        "default": "backups/vpn",
                        "description": "Directory for configuration backups"
                    },
                    "max_backups": {
                        "type": "integer",
                        "default": 10,
                        "description": "Maximum number of backups to keep per server"
                    }
                }
            },
            "vpn_api": {
                "type": "object",
                "properties": {
                    "base_url": {
                        "type": "string",
                        "default": "https://blueblue.api.online-mtyb.com",
                        "description": "Base URL for VPN API service"
                    },
                    "username": {
                        "type": "string",
                        "default": "admin",
                        "description": "API username for authentication"
                    },
                    "password": {
                        "type": "string",
                        "default": "admin123",
                        "description": "API password for authentication"
                    },
                    "timeout": {
                        "type": "integer",
                        "default": 30,
                        "description": "API request timeout in seconds"
                    },
                    "retry_attempts": {
                        "type": "integer",
                        "default": 3,
                        "description": "Number of retry attempts for failed requests"
                    },
                    "retry_delay": {
                        "type": "integer",
                        "default": 5,
                        "description": "Delay between retry attempts in seconds"
                    }
                }
            },
            "redemption": {
                "type": "object",
                "properties": {
                    "enabled": {
                        "type": "boolean",
                        "default": True,
                        "description": "Enable VPN redemption functionality"
                    },
                    "default_expiry_days": {
                        "type": "integer",
                        "default": 30,
                        "description": "Default expiry period for VPN accounts"
                    },
                    "code_prefix": {
                        "type": "string",
                        "default": "VPN",
                        "description": "Prefix for redemption codes"
                    },
                    "code_length": {
                        "type": "integer",
                        "default": 12,
                        "description": "Length of redemption codes"
                    }
                }
            },
            "monitoring": {
                "type": "object",
                "properties": {
                    "enable_auto_health_check": {
                        "type": "boolean",
                        "default": True,
                        "description": "Enable automatic health checks"
                    },
                    "health_check_cron": {
                        "type": "string",
                        "default": "*/5 * * * *",
                        "description": "Cron expression for health checks"
                    },
                    "enable_expiry_notifications": {
                        "type": "boolean",
                        "default": True,
                        "description": "Enable client expiry notifications"
                    },
                    "notification_channels": {
                        "type": "array",
                        "default": ["email", "webhook"],
                        "description": "Notification channels for alerts"
                    }
                }
            }
        }
        
    def get_admin_routes(self) -> Dict[str, str]:
        """Return admin interface routes for this plugin"""
        return {
            "VPN Dashboard": "/admin/vpn/health",
            "VPN Servers": "/admin/vpn/servers",
            "VPN Clients": "/admin/vpn/clients",
            "Client Sync": "/admin/vpn/clients/sync",
            "SSH User Management": "/admin/vpn/ssh-users",
            "VPN Configurations": "/admin/vpn/configurations",
            "WebSocket Monitor": "/admin/vpn/websocket",
            "Background Tasks": "/admin/vpn/tasks",
            "VPN Settings": "/admin/vpn/settings",
            "VPN Redemption": "/admin/vpn/redemption/health"
        }
        
    def get_api_info(self) -> Dict[str, Any]:
        """Return API information for this plugin"""
        return {
            "name": "VPN Management API",
            "version": "2.0",
            "endpoints": {
                "servers": {
                    "list": "/admin/vpn/api/servers",
                    "health": "/admin/vpn/api/health/server/{server_id}"
                },
                "clients": {
                    "list": "/admin/vpn/api/clients",
                    "sync": "/admin/vpn/api/sync/compare/{server_id}"
                },
                "ssh_users": {
                    "create": "/admin/vpn/api/servers/{server_id}/ssh-users",
                    "members": "/admin/vpn/api/servers/{server_id}/ssh-users/members",
                    "sessions": "/admin/vpn/api/servers/{server_id}/ssh-users/sessions",
                    "renew": "/admin/vpn/api/servers/{server_id}/ssh-users/renew",
                    "auto_delete": "/admin/vpn/api/servers/{server_id}/ssh-users/auto-delete-expired",
                    "limit_violations": "/admin/vpn/api/servers/{server_id}/ssh-users/limit-violations",
                    "autokill_status": "/admin/vpn/api/servers/{server_id}/ssh-users/autokill/status",
                    "autokill_configure": "/admin/vpn/api/servers/{server_id}/ssh-users/autokill/configure"
                },
                "authentication": {
                    "register": "/admin/vpn/api/auth/register",
                    "current_user": "/admin/vpn/api/auth/me",
                    "logout": "/admin/vpn/api/auth/logout",
                    "refresh": "/admin/vpn/api/auth/refresh"
                },
                "monitoring": {
                    "websocket_stats": "/admin/vpn/api/websocket/stats",
                    "connection_test": "/admin/vpn/api/test-connection"
                },
                "tasks": {
                    "enqueue": "/admin/vpn/api/background-tasks/enqueue"
                },
                "info": {
                    "root": "/admin/vpn/api/info/root",
                    "version": "/admin/vpn/api/info/version"
                }
            },
            "features": [
                "Complete server management (CRUD)",
                "Client management with bulk operations",
                "SSH user management and monitoring",
                "SSH session tracking and autokill configuration",
                "Automatic expired user cleanup",
                "Configuration backup and restore",
                "Real-time health monitoring",
                "Automatic expiry management",
                "SSH connection pooling",
                "Configuration validation",
                "Sync between database and servers",
                "User authentication management",
                "WebSocket monitoring and statistics",
                "Background task management",
                "API information endpoints"
            ]
        }
        
    def get_statistics(self) -> Dict[str, Any]:
        """Get plugin statistics"""
        try:
            stats = {
                "servers": {"total": 0, "active": 0, "healthy": 0},
                "clients": {"total": 0, "active": 0, "expired": 0},
                "health": {"last_check": None, "status": "unknown"}
            }
            
            # Get server stats
            servers = self.api_service.get_servers()
            if servers:
                stats["servers"]["total"] = len(servers)
                stats["servers"]["active"] = sum(1 for s in servers if s.get("is_active"))
                
            # Get client stats
            clients_data = self.api_service.get_clients(limit=1)
            if clients_data:
                stats["clients"]["total"] = clients_data.get("total", 0)
                
            # Get health stats
            health = self.api_service.get_health_dashboard()
            if health:
                stats["health"]["status"] = health.get("overall_health", "unknown")
                stats["health"]["last_check"] = health.get("last_check")
                stats["servers"]["healthy"] = health.get("healthy_servers", 0)
                
            return stats
            
        except Exception as e:
            logger.error(f"Error getting plugin statistics: {e}")
            return {}
