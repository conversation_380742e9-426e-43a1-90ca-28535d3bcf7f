"""
Core Plugin
Provides core dashboard widgets and functionality
"""

from flask import Blueprint, jsonify, request
from core.plugin_manager import PluginInterface, DashboardWidget
from utils.credential_manager import credential_manager
from config import load_config, save_config
from services.session_service import session_manager
from services.netflix_session_service import netflix_session_manager
from datetime import datetime, timedelta
from collections import defaultdict
import json
import os
import logging

logger = logging.getLogger(__name__)


class Plugin(PluginInterface):
    """Core plugin providing essential dashboard widgets"""
    
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.name = "Core Dashboard"
        self.version = "1.0.0"
        self.description = "Provides core dashboard widgets and statistics"
        self.blueprint = Blueprint('core', __name__)
        self._setup_routes()
        
    def initialize(self) -> bool:
        """Initialize the plugin"""
        try:
            logger.info("Core plugin initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize core plugin: {e}")
            return False
            
    def shutdown(self) -> bool:
        """Shutdown the plugin"""
        return True
        
    def get_blueprint(self) -> Blueprint:
        """Return the plugin's blueprint"""
        return self.blueprint
        
    def get_config_schema(self) -> dict:
        """Return configuration schema"""
        return {
            "type": "object",
            "properties": {
                "refresh_interval": {
                    "type": "integer",
                    "default": 300,
                    "description": "Widget refresh interval in seconds"
                }
            }
        }
        
    def get_dashboard_widgets(self) -> list:
        """Return list of dashboard widgets provided by this plugin"""
        widgets = []
        
        # Core statistics widget
        core_stats = DashboardWidget(
            widget_id="core-stats",
            title="Core Statistics",
            position="header",
            order=10,
            size="full"
        )
        core_stats.data_endpoint = "/api/core/widget/stats"
        core_stats.refresh_interval = 300  # 5 minutes
        widgets.append(core_stats)
        
        # Health status widget
        health_widget = DashboardWidget(
            widget_id="health-status",
            title="API Health Status",
            position="main",
            order=20,
            size="full"
        )
        health_widget.data_endpoint = "/api/core/widget/health"
        health_widget.refresh_interval = 60  # 1 minute
        widgets.append(health_widget)
        
        # Redeem statistics widget
        redeem_stats = DashboardWidget(
            widget_id="redeem-stats",
            title="Redeem Statistics",
            position="main",
            order=30,
            size="large"
        )
        redeem_stats.data_endpoint = "/api/core/widget/redeem-stats"
        redeem_stats.refresh_interval = 300  # 5 minutes
        widgets.append(redeem_stats)
        
        # Auth code records widget
        auth_records = DashboardWidget(
            widget_id="auth-code-records",
            title="Authentication Code Records",
            position="main",
            order=50,
            size="full"
        )
        auth_records.data_endpoint = "/api/core/widget/auth-records"
        auth_records.refresh_interval = None  # No auto-refresh
        widgets.append(auth_records)
        
        return widgets
        
    def get_widget_data(self, widget_id: str) -> dict:
        """Get data for a specific widget"""
        if widget_id == "core-stats":
            return self._get_core_stats()
        elif widget_id == "health":
            return self._get_health_status()
        elif widget_id == "redeem-stats":
            return self._get_redeem_stats()
        elif widget_id == "auth-records":
            return self._get_auth_records()
        else:
            return {"error": f"Unknown widget: {widget_id}"}
            
    def _setup_routes(self):
        """Set up plugin routes"""
        
        @self.blueprint.route('/widget/stats')
        def widget_stats():
            data = self._get_core_stats()
            return jsonify({"success": True, "data": data})
            
        @self.blueprint.route('/widget/health')
        def widget_health():
            # Use the health monitoring service if available
            try:
                from services.health_monitoring_service import health_monitoring_service
                health_data = health_monitoring_service.get_overall_health_summary()
                return jsonify({"success": True, "data": health_data})
            except Exception as e:
                logger.error(f"Error getting health status: {e}")
                return jsonify({"success": False, "error": str(e)})
                
        @self.blueprint.route('/widget/redeem-stats')
        def widget_redeem_stats():
            data = self._get_redeem_stats()
            return jsonify({"success": True, "data": data})
            
        @self.blueprint.route('/widget/auth-records')
        def widget_auth_records():
            data = self._get_auth_records()
            return jsonify({"success": True, "data": data})
            
        @self.blueprint.route('/stats')
        def get_stats():
            """Legacy endpoint for backward compatibility"""
            data = self._get_core_stats()
            return jsonify({"success": True, "data": data})
            
    def _get_core_stats(self) -> dict:
        """Get core statistics data"""
        try:
            # Get email config count
            email_config_count = credential_manager.get_email_count()
            
            # Get Steam credential count
            steam_credential_count = credential_manager.get_credential_count()
            
            # Get successful redeems today
            dashboard_data = self._load_dashboard_data()
            today = datetime.now().strftime('%Y-%m-%d')
            daily_stats = dashboard_data.get('daily_stats', {})
            today_stats = daily_stats.get(today, {})
            successful_redeems_today = today_stats.get('successful_requests', 0)
            
            return {
                "email_config_count": email_config_count,
                "steam_credential_count": steam_credential_count,
                "successful_redeems_today": successful_redeems_today
            }
        except Exception as e:
            logger.error(f"Error getting core stats: {e}")
            return {
                "email_config_count": 0,
                "steam_credential_count": 0,
                "successful_redeems_today": 0
            }
            
    def _get_redeem_stats(self) -> dict:
        """Get redeem statistics"""
        try:
            dashboard_data = self._load_dashboard_data()
            
            # Calculate statistics from auth code records
            account_redeems = defaultdict(int)
            order_redeems = defaultdict(int)
            
            for record in dashboard_data.get('auth_code_records', []):
                if record.get('status') == 'Success':
                    account_redeems[record.get('username', 'Unknown')] += 1
                    order_redeems[record.get('order_id', 'Unknown')] += 1
                    
            return {
                "account_redeems": dict(account_redeems),
                "order_redeems": dict(order_redeems)
            }
        except Exception as e:
            logger.error(f"Error getting redeem stats: {e}")
            return {
                "account_redeems": {},
                "order_redeems": {}
            }
            
    def _get_auth_records(self) -> dict:
        """Get authentication code records"""
        try:
            dashboard_data = self._load_dashboard_data()
            records = dashboard_data.get('auth_code_records', [])
            return records
        except Exception as e:
            logger.error(f"Error getting auth records: {e}")
            return []
            
    def _load_dashboard_data(self) -> dict:
        """Load dashboard data from file"""
        file_path = 'configs/data/dashboard_data.json'
        default_data = {
            'auth_code_records': [],
            'daily_stats': {},
            'last_update': None
        }
        
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading dashboard data: {e}")
                return default_data
        else:
            return default_data 