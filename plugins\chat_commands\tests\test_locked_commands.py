import os
import sys
import tempfile
from flask import Flask, session
import pytest

# Ensure repository root is on sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from plugins.chat_commands.models import Cha<PERSON><PERSON><PERSON>mand
from plugins.chat_commands.services import ChatCommandService
from plugins.chat_commands.routes import create_routes


@pytest.fixture
def temp_dir():
    d = tempfile.mkdtemp()
    try:
        yield d
    finally:
        import shutil
        shutil.rmtree(d, ignore_errors=True)


def test_service_blocks_update_delete_on_locked(temp_dir):
    service = ChatCommandService(temp_dir)

    # Register a locked external command
    cmd = ChatCommand(
        command="sshadd",
        description="Create SSH user",
        response_text="",
        enabled=True,
        locked=True,
    )
    # Simulate external registration; direct assignment to mimic persistence
    service._commands[cmd.command] = cmd

    # Attempt update
    updated = service.update_command("sshadd", ChatCommand(
        command="sshadd",
        description="Changed",
        response_text="",
        enabled=True,
    ))
    assert updated is False
    assert service.get_command("sshadd").description == "Create SSH user"

    # Attempt delete
    deleted = service.delete_command("sshadd")
    assert deleted is False
    assert service.get_command("sshadd") is not None


def test_routes_return_403_for_locked_commands(temp_dir):
    # Build minimal plugin_instance stub with command_service
    class PluginStub:
        def __init__(self, command_service):
            self.command_service = command_service
            self.message_processor = None

    service = ChatCommandService(temp_dir)

    # Seed a locked command
    locked_cmd = ChatCommand(
        command="sshuser",
        description="Create SSH user",
        response_text="",
        enabled=True,
        locked=True,
    )
    service._commands[locked_cmd.command] = locked_cmd

    plugin_stub = PluginStub(service)
    bp = create_routes(plugin_stub)

    app = Flask(__name__)
    app.secret_key = "test-secret"
    app.register_blueprint(bp)

    with app.test_client() as client:
        # login (set admin_logged_in in session)
        with client.session_transaction() as sess:
            sess['admin_logged_in'] = True

        # Try to update locked command
        resp = client.put(
            f"/chat-commands/api/commands/{locked_cmd.command}",
            json={
                "command": "sshuser",
                "description": "Changed",
                "response_text": "",
                "enabled": True,
            },
        )
        assert resp.status_code == 403
        assert service.get_command("sshuser").description == "Create SSH user"

        # Try to delete locked command
        resp2 = client.delete(f"/chat-commands/api/commands/{locked_cmd.command}")
        assert resp2.status_code == 403
        assert service.get_command("sshuser") is not None

