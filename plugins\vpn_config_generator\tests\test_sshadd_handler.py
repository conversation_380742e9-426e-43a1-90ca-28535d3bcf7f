import os
import sys
import types
import pytest

# Ensure repository root is on sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from plugins.vpn_config_generator.plugin import Plugin as VPNGenPlugin


class DummyVPNAPI:
    def __init__(self):
        self.calls = []

    def create_ssh_user(self, server_id, username, password, expires_in_days=None, expiry_date=None, shell='/bin/false', create_home=False):
        self.calls.append({
            'server_id': server_id,
            'username': username,
            'password': password,
            'expires_in_days': expires_in_days,
            'expiry_date': expiry_date,
        })
        # Simulate API response
        return {
            'success': True,
            'username': username,
            'server_id': server_id,
            'expires_on': '2025-01-01'
        }


class DummyConfigService:
    def __init__(self, vpn_api):
        self._vpn_api_service = vpn_api

    def _init_vpn_service(self):
        # already set
        pass


class DummyPluginManager:
    def get_plugin(self, name):
        return None


@pytest.fixture
def plugin_with_dummy_api():
    pm = DummyPluginManager()
    plugin = VPNGenPlugin(pm)
    plugin.config_service = DummyConfigService(DummyVPNAPI())
    return plugin


def test_sshadd_parsing_and_api_call(plugin_with_dummy_api):
    plugin = plugin_with_dummy_api

    # Invoke handler: #sshadd 1 alice pass123 7
    responses = plugin._handle_sshadd_command(
        message=types.SimpleNamespace(sender_name='tester'),
        params=['1', 'alice', 'pass123', '7']
    )

    assert responses and len(responses) == 1
    text = responses[0].text
    assert "Created SSH user 'alice' on server 1" in text

    # Check API call
    calls = plugin.config_service._vpn_api_service.calls
    assert len(calls) == 1
    call = calls[0]
    assert call['server_id'] == 1
    assert call['username'] == 'alice'
    assert call['password'] == 'pass123'
    assert call['expires_in_days'] == 7


def test_sshadd_usage_errors(plugin_with_dummy_api):
    plugin = plugin_with_dummy_api

    # Missing params
    r1 = plugin._handle_sshadd_command(message=None, params=['1', 'alice', 'pass'])
    assert "Usage:" in r1[0].text

    # Non-numeric server_id
    r2 = plugin._handle_sshadd_command(message=None, params=['x', 'alice', 'pass', '7'])
    assert "server_id must be a number" in r2[0].text

    # Non-integer days
    r3 = plugin._handle_sshadd_command(message=None, params=['1', 'alice', 'pass', 'x'])
    assert "days must be an integer" in r3[0].text

    # Non-positive days
    r4 = plugin._handle_sshadd_command(message=None, params=['1', 'alice', 'pass', '0'])
    assert "positive integer" in r4[0].text

