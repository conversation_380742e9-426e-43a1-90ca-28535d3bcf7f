{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block header %}{{ title }}{% endblock %}

{% block content %}
<div class="bg-white rounded-lg shadow-lg p-6">
    <!-- Header Section -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">VPN Servers Management</h2>
            <p class="text-gray-600 mt-1">Manage your VPN servers, monitor health, and configure settings</p>
        </div>
        <div class="flex space-x-3">
            <button id="refresh-all-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-sync-alt mr-2"></i>Refresh All
            </button>
            <a href="{{ url_for('vpn.create_server') }}" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-plus mr-2"></i>Add Server
            </a>
        </div>
    </div>

    <!-- Servers Table -->
    {% if servers %}
    <div class="table-container overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Server</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Health</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clients</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for server in servers %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center">
                                    <i class="fas fa-server text-white"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">{{ server.name }}</div>
                                <div class="text-sm text-gray-500">{{ server.host }}:{{ server.port }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <label class="inline-flex items-center">
                                <input type="checkbox" class="form-checkbox h-5 w-5 text-green-600 server-active-toggle" 
                                       data-server-id="{{ server.id }}" 
                                       {% if server.is_active %}checked{% endif %}>
                                <span class="ml-2 text-sm text-gray-700">
                                    {% if server.is_active %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <span class="w-2 h-2 mr-1 bg-green-400 rounded-full"></span>
                                            Active
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <span class="w-2 h-2 mr-1 bg-red-400 rounded-full"></span>
                                            Inactive
                                        </span>
                                    {% endif %}
                                </span>
                            </label>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                         <div id="health-{{ server.id }}">
                             <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                 <i class="fas fa-spinner fa-spin mr-1"></i>Checking...
                             </span>
                         </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                         <div id="clients-{{ server.id }}">
                             <i class="fas fa-spinner fa-spin text-gray-400"></i>
                         </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt text-gray-400 mr-2"></i>
                            {{ server.location or 'Unknown' }}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex items-center space-x-2">
                            <!-- Quick Actions -->
                            <button onclick="testConnection({{ server.id }})" 
                                    class="text-blue-600 hover:text-blue-900 transition duration-200" 
                                    title="Test Connection">
                                <i class="fas fa-plug"></i>
                            </button>
                            <button onclick="refreshHealth({{ server.id }})" 
                                    class="text-green-600 hover:text-green-900 transition duration-200" 
                                    title="Refresh Health">
                                <i class="fas fa-heartbeat"></i>
                            </button>
                            
                            <!-- Dropdown Menu -->
                            <div class="dropdown-container">
                                <button onclick="toggleDropdown({{ server.id }})"
                                        id="dropdown-btn-{{ server.id }}"
                                        class="dropdown-trigger text-gray-600 hover:text-gray-900 transition duration-200 p-2 rounded hover:bg-gray-100"
                                        title="More Actions">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-12">
        <div class="mx-auto h-24 w-24 text-gray-400">
            <i class="fas fa-server text-6xl"></i>
        </div>
        <h3 class="mt-4 text-lg font-medium text-gray-900">No servers configured</h3>
        <p class="mt-2 text-gray-500">Get started by adding your first VPN server.</p>
        <div class="mt-6">
            <a href="{{ url_for('vpn.create_server') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 transition duration-200">
                <i class="fas fa-plus mr-2"></i>Add Your First Server
            </a>
        </div>
    </div>
    {% endif %}
</div>

<!-- Server Stats Summary -->
{% if servers %}
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <i class="fas fa-server text-white"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Servers</p>
                <p class="text-2xl font-semibold text-gray-900">{{ servers|length }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <i class="fas fa-check-circle text-white"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Active Servers</p>
                <p class="text-2xl font-semibold text-gray-900">{{ servers|selectattr('is_active')|list|length }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <i class="fas fa-users text-white"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Clients</p>
                <p class="text-2xl font-semibold text-gray-900" id="total-clients">-</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <i class="fas fa-heartbeat text-white"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Healthy Servers</p>
                <p class="text-2xl font-semibold text-gray-900" id="healthy-servers">-</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Global Dropdown Menu -->
<div id="global-dropdown-menu" class="fixed hidden bg-white rounded-md shadow-lg border border-gray-200 py-1 min-w-48 z-50" style="display: none;">
    <a id="dropdown-edit" href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
        <i class="fas fa-edit mr-2"></i>Edit
    </a>
    <button id="dropdown-restart" onclick="handleDropdownAction('restart')"
            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
        <i class="fas fa-redo mr-2"></i>Restart Xray
    </button>
    <button id="dropdown-sync" onclick="handleDropdownAction('sync')"
            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
        <i class="fas fa-sync-alt mr-2"></i>Sync Clients
    </button>
    <button id="dropdown-remove-expired" onclick="handleDropdownAction('removeExpired')"
            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
        <i class="fas fa-user-times mr-2"></i>Remove Expired
    </button>
    <div class="border-t border-gray-100"></div>
    <button id="dropdown-delete" onclick="handleDropdownAction('delete')"
            class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
        <i class="fas fa-trash mr-2"></i>Delete
    </button>
</div>

<!-- Delete Confirmation Modal -->
<div x-data="$store.deleteModal">
    <div x-show="showDeleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" 
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mt-4">Confirm Delete</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500">
                        Are you sure you want to delete server "<span x-text="serverNameToDelete"></span>"?
                    </p>
                    <p class="text-sm text-red-600 mt-2">This action cannot be undone!</p>
                </div>
                <div class="flex justify-center space-x-4 mt-4">
                    <button @click="showDeleteModal = false" 
                            class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 transition duration-200">
                        Cancel
                    </button>
                    <form :action="'/admin/vpn/servers/' + serverToDelete + '/delete'" method="POST" class="inline">
                        <button type="submit" 
                                class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 transition duration-200">
                            Delete Server
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Initialize Alpine.js store for delete modal
        document.addEventListener('alpine:init', () => {
            Alpine.store('deleteModal', {
                showDeleteModal: false,
                serverToDelete: null,
                serverNameToDelete: ''
            });
        });
        
        window.deleteServer = function(serverId, serverName) {
            Alpine.store('deleteModal').showDeleteModal = true;
            Alpine.store('deleteModal').serverToDelete = serverId;
            Alpine.store('deleteModal').serverNameToDelete = serverName;
        }
    </script>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

<style>
/* Fix table overflow for better dropdown positioning */
.table-container {
    position: relative;
    overflow: visible !important;
}

/* Global dropdown menu styling */
#global-dropdown-menu {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Dropdown container and trigger styling */
.dropdown-container {
    position: relative;
    display: inline-block;
}

.dropdown-trigger {
    cursor: pointer;
    border: none;
    background: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.dropdown-trigger:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Ensure table doesn't interfere with dropdown positioning */
tbody tr {
    position: static;
}

table {
    overflow: visible;
}
</style>

<script>
// Configure toastr
toastr.options = {
    "closeButton": true,
    "progressBar": true,
    "positionClass": "toast-top-right",
    "timeOut": "3000"
};

// Toggle server active status
$(document).on('change', '.server-active-toggle', function() {
    var serverId = $(this).data('server-id');
    var isActive = $(this).prop('checked');

    $.ajax({
        url: `/admin/vpn/servers/${serverId}/edit`,
        method: 'POST',
        data: { is_active: isActive ? 'on' : 'off' },
        success: function() {
            toastr.success('Server status updated');
        },
        error: function() {
            toastr.error('Failed to update server status');
            // Revert the toggle
            $(this).prop('checked', !isActive);
        }
    });
});

function testConnection(serverId) {
    toastr.info('Testing connection...');
    $.ajax({
        url: `/admin/vpn/servers/${serverId}/test-connection`,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        success: function(data) {
            if (data.success) {
                toastr.success('Connection successful!');
            } else {
                toastr.error('Connection failed: ' + (data.message || 'Unknown error'));
            }
        },
        error: function(xhr, status, error) {
            console.error('Connection test error:', xhr.responseText);
            toastr.error('Failed to test connection: ' + error);
        }
    });
}

function refreshHealth(serverId) {
    // Show refreshing state
    $(`#health-${serverId}`).html('<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"><i class="fas fa-spinner fa-spin mr-1"></i>Refreshing...</span>');
    $(`#clients-${serverId}`).html('<i class="fas fa-spinner fa-spin text-gray-400"></i>');

    toastr.info('Refreshing health status...');

    // First trigger the refresh, then load the updated health data
    $.ajax({
        url: `/admin/vpn/api/health/servers/${serverId}/refresh`,
        method: 'POST',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        success: function(data) {
            // After refresh, load the updated health data
            loadServerHealth(serverId);
            toastr.success('Health status refreshed');
        },
        error: function() {
            // Still try to load health status
            loadServerHealth(serverId);
            toastr.error('Failed to refresh health status');
        }
    });
}

function refreshAllHealth() {
    // Show refreshing state for all servers
    {% for server in servers %}
    $(`#health-{{ server.id }}`).html('<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"><i class="fas fa-spinner fa-spin mr-1"></i>Refreshing...</span>');
    $(`#clients-{{ server.id }}`).html('<i class="fas fa-spinner fa-spin text-gray-400"></i>');
    {% endfor %}
    
    toastr.info('Refreshing all servers health...');
    $.ajax({
        url: '/admin/vpn/api/health/refresh-all-servers',
        method: 'POST',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        success: function(data) {
            toastr.success('Health refresh triggered for all servers');
            // Load health status for each server with delay
            {% for server in servers %}
            setTimeout(() => loadServerHealth({{ server.id }}), {{ loop.index0 * 500 }});
            {% endfor %}
        },
        error: function() {
            toastr.error('Failed to refresh health status');
            // Still load health status for each server
            {% for server in servers %}
            setTimeout(() => loadServerHealth({{ server.id }}), {{ loop.index0 * 500 }});
            {% endfor %}
        }
    });
}

function loadServerHealth(serverId) {
    console.log(`Loading health for server ${serverId}`);

    // Use the GET health endpoint that returns complete health data with client statistics
    $.ajax({
        url: `/admin/vpn/api/health/server/${serverId}`,
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        success: function(response) {
            console.log(`Health response for server ${serverId}:`, response);
            if (response && response.success && response.health) {
                const healthData = response.health;
                console.log(`Updating display for server ${serverId} with health:`, healthData);
                updateHealthDisplay(serverId, healthData);
                updateClientsDisplay(serverId, healthData);
            } else {
                console.error(`Invalid health response for server ${serverId}:`, response);
                // Show error state
                $(`#health-${serverId}`).html('<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"><i class="fas fa-times mr-1"></i>Error</span>');
                $(`#clients-${serverId}`).html('<span class="text-gray-400">-</span>');
            }
        },
        error: function(xhr, status, error) {
            console.error(`Error loading server health for ${serverId}:`, {
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText,
                error: error
            });
            // Show error state
            $(`#health-${serverId}`).html('<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"><i class="fas fa-times mr-1"></i>Error</span>');
            $(`#clients-${serverId}`).html('<span class="text-gray-400">-</span>');
        }
    });
}

function updateHealthDisplay(serverId, healthData) {
    var healthDiv = $(`#health-${serverId}`);

    // Handle both API response formats
    var isHealthy = healthData.is_healthy || (healthData.status === 'healthy');

    if (isHealthy === true) {
        healthDiv.html('<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"><i class="fas fa-check-circle mr-1"></i>Healthy</span>');
    } else if (isHealthy === false) {
        healthDiv.html('<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"><i class="fas fa-exclamation-circle mr-1"></i>Unhealthy</span>');
    } else {
        healthDiv.html('<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"><i class="fas fa-question-circle mr-1"></i>Unknown</span>');
    }
}

function updateClientsDisplay(serverId, healthData) {
    var clientsDiv = $(`#clients-${serverId}`);
    
    // Handle both client_stats and direct count properties
    var stats = healthData.client_stats || {
        total: healthData.client_count || 0,
        active: healthData.active_clients || 0,
        expired: (healthData.client_count || 0) - (healthData.active_clients || 0)
    };
    
    if (stats.total > 0 || stats.total === 0) {
        var html = '<div class="flex items-center space-x-2">';
        html += `<span class="font-medium">${stats.total}</span>`;
        html += '<span class="text-gray-500">total</span>';
        if (stats.expired > 0) {
            html += `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">${stats.expired} expired</span>`;
        }
        html += '</div>';
        clientsDiv.html(html);
    } else {
        clientsDiv.html('<span class="text-gray-400">-</span>');
    }
}

function restartXray(serverId) {
    if (confirm('Are you sure you want to restart Xray service?')) {
        toastr.info('Restarting Xray service...');
        $.post(`/admin/vpn/servers/${serverId}/restart-xray`, function() {
            toastr.success('Xray service restarted successfully');
        }).fail(function() {
            toastr.error('Failed to restart Xray service');
        });
    }
}

function syncClients(serverId) {
    toastr.info('Syncing clients...');
    $.post(`/admin/vpn/api/sync/server/${serverId}`, function(data) {
        toastr.success('Clients synced successfully');
    }).fail(function() {
        toastr.error('Failed to sync clients');
    });
}

function removeExpired(serverId) {
    if (confirm('Are you sure you want to remove all expired clients from this server?')) {
        toastr.info('Removing expired clients...');
        $.post(`/admin/vpn/servers/${serverId}/remove-expired`, function() {
            toastr.success('Expired clients removed successfully');
        }).fail(function() {
            toastr.error('Failed to remove expired clients');
        });
    }
}

// Global dropdown functionality
let currentDropdownServerId = null;

function toggleDropdown(serverId) {
    const dropdown = document.getElementById('global-dropdown-menu');
    const button = document.getElementById(`dropdown-btn-${serverId}`);

    // If clicking the same button, hide dropdown
    if (currentDropdownServerId === serverId && dropdown.style.display === 'block') {
        hideDropdown();
        return;
    }

    // Update current server ID
    currentDropdownServerId = serverId;

    // Update edit link
    const editLink = document.getElementById('dropdown-edit');
    editLink.href = `/admin/vpn/servers/${serverId}/edit`;

    // Position dropdown near the button (fixed positioning uses viewport coordinates)
    const buttonRect = button.getBoundingClientRect();

    // Show the dropdown invisibly to measure actual size
    const prevVisibility = dropdown.style.visibility;
    const prevDisplay = dropdown.style.display;
    dropdown.style.visibility = 'hidden';
    dropdown.style.display = 'block';
    const dropdownWidth = dropdown.offsetWidth || 192;   // fallback to w-48 (12rem)
    const dropdownHeight = dropdown.offsetHeight || 200; // approximate fallback

    // Preferred position: below the trigger and right-aligned
    let top = buttonRect.bottom + 5;
    let left = buttonRect.right - dropdownWidth;

    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Keep within viewport horizontally
    if (left + dropdownWidth > viewportWidth - 10) {
        left = Math.max(10, viewportWidth - dropdownWidth - 10);
    }
    if (left < 10) {
        left = 10;
    }

    // If not enough space below, place above
    if (top + dropdownHeight > viewportHeight - 10) {
        top = buttonRect.top - dropdownHeight - 5;
    }
    if (top < 10) {
        top = 10;
    }

    // Apply position and show
    dropdown.style.left = left + 'px';
    dropdown.style.top = top + 'px';
    dropdown.style.visibility = 'visible';
    dropdown.style.display = 'block';
    dropdown.classList.remove('hidden');
}

function hideDropdown() {
    const dropdown = document.getElementById('global-dropdown-menu');
    dropdown.style.display = 'none';
    dropdown.classList.add('hidden');
    currentDropdownServerId = null;
}

function handleDropdownAction(action) {
    if (!currentDropdownServerId) return;

    const serverId = currentDropdownServerId;
    hideDropdown();

    switch(action) {
        case 'restart':
            restartXray(serverId);
            break;
        case 'sync':
            syncClients(serverId);
            break;
        case 'removeExpired':
            removeExpired(serverId);
            break;
        case 'delete':
            // Get server name for confirmation
            const serverRow = document.querySelector(`#dropdown-btn-${serverId}`).closest('tr');
            const serverName = serverRow.querySelector('.text-sm.font-medium.text-gray-900').textContent;
            deleteServer(serverId, serverName);
            break;
    }
}

// Hide dropdown when clicking outside
document.addEventListener('click', function(event) {
    const isDropdownButton = event.target.closest('.dropdown-trigger');
    const isDropdownMenu = event.target.closest('#global-dropdown-menu');

    if (!isDropdownButton && !isDropdownMenu) {
        hideDropdown();
    }
});

// Hide dropdown on scroll
window.addEventListener('scroll', function() {
    hideDropdown();
});

// Hide dropdown on window resize
window.addEventListener('resize', function() {
    hideDropdown();
});



// Calculate totals on page load and start health loading
$(document).ready(function() {
    console.log('VPN Servers page loaded, starting health checks...');
    
    // Bind refresh all button
    $('#refresh-all-btn').on('click', function() {
        refreshAllHealth();
    });
    
    // Initialize totals
    $('#total-clients').text('-');
    $('#healthy-servers').text('-');
    
    // Load health status for all servers when page loads
    {% for server in servers %}
    // Add delay between requests to avoid overwhelming the server
    setTimeout(() => {
        console.log('Loading health for server {{ server.id }}');
        loadServerHealth({{ server.id }});
    }, {{ loop.index0 * 500 }});
    {% endfor %}
    
    // Recalculate totals after health checks complete
    setTimeout(function() {
        calculateTotals();
    }, {{ servers|length * 500 + 2000 }});
});

function calculateTotals() {
    var totalClients = 0;
    var healthyServers = 0;
    
    {% for server in servers %}
    // Parse health data from DOM to calculate totals
    const healthCell{{ server.id }} = $(`#health-{{ server.id }}`).text();
    if (healthCell{{ server.id }}.includes('Healthy')) {
        healthyServers++;
    }
    
    const clientsCell{{ server.id }} = $(`#clients-{{ server.id }}`).text();
    const totalMatch{{ server.id }} = clientsCell{{ server.id }}.match(/(\d+)\s+total/);
    if (totalMatch{{ server.id }}) {
        totalClients += parseInt(totalMatch{{ server.id }}[1]);
    }
    {% endfor %}
    
    $('#total-clients').text(totalClients);
    $('#healthy-servers').text(healthyServers);
}
</script>
{% endblock %}
