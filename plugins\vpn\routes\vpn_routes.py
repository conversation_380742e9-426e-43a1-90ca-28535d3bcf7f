"""
VPN Routes - Complete implementation for all VPN management endpoints
"""

import logging
import os
import json
import time
import socket
from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, session
from functools import wraps
from datetime import datetime

logger = logging.getLogger(__name__)
# Try to import paramiko for SSH testing
try:
    import paramiko
    PARAMIKO_AVAILABLE = True
except ImportError:
    PARAMIKO_AVAILABLE = False
    logger.warning("Paramiko not available - SSH testing will be limited")

def load_vpn_config():
    """Load VPN plugin configuration"""
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'configs', 'plugins', 'vpn', 'config.json')
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        logger.warning(f"Could not load VPN config: {e}")

    # Default configuration
    return {
        "auto_restart_xray": {
            "enabled": True,
            "restart_delay_seconds": 2,
            "max_retry_attempts": 3,
            "show_restart_notifications": True
        }
    }

def auto_restart_xray_service(api_service, server_id: int, operation_description: str = "client changes") -> bool:
    """
    Automatically restart Xray service after client changes if enabled in config

    Args:
        api_service: VPN API service instance
        server_id: ID of the server to restart
        operation_description: Description of the operation that triggered restart

    Returns:
        bool: True if restart was successful or not needed, False if restart failed
    """
    config = load_vpn_config()
    auto_restart_config = config.get("auto_restart_xray", {})

    if not auto_restart_config.get("enabled", True):
        logger.info(f"Auto-restart disabled for server {server_id}")
        return True

    try:
        # Add delay before restart if configured
        delay = auto_restart_config.get("restart_delay_seconds", 2)
        if delay > 0:
            time.sleep(delay)

        # Attempt restart with retries
        max_attempts = auto_restart_config.get("max_retry_attempts", 3)

        for attempt in range(1, max_attempts + 1):
            logger.info(f"Attempting to restart Xray service on server {server_id} (attempt {attempt}/{max_attempts}) after {operation_description}")

            restart_success = api_service.restart_xray_service(server_id)

            if restart_success:
                if auto_restart_config.get("show_restart_notifications", True):
                    flash(f'✅ Xray service restarted successfully on server {server_id} after {operation_description}', 'success')
                logger.info(f"Successfully restarted Xray service on server {server_id}")
                return True
            else:
                logger.warning(f"Failed to restart Xray service on server {server_id} (attempt {attempt}/{max_attempts})")
                if attempt < max_attempts:
                    time.sleep(1)  # Wait before retry

        # All attempts failed
        if auto_restart_config.get("show_restart_notifications", True):
            flash(f'⚠️ Failed to restart Xray service on server {server_id}. Please restart manually to apply {operation_description}.', 'warning')
        logger.error(f"Failed to restart Xray service on server {server_id} after {max_attempts} attempts")
        return False

    except Exception as e:
        logger.error(f"Error during auto-restart of Xray service on server {server_id}: {e}")
        if auto_restart_config.get("show_restart_notifications", True):
            flash(f'❌ Error restarting Xray service on server {server_id}: {str(e)}', 'error')
        return False

def test_basic_connectivity(host: str, port: int) -> dict:
    """Test basic TCP connectivity to host:port"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()

        if result == 0:
            return {
                'success': True,
                'message': f'TCP connection to {host}:{port} successful',
                'test_method': 'Basic TCP'
            }
        else:
            return {
                'success': False,
                'message': f'TCP connection to {host}:{port} failed',
                'test_method': 'Basic TCP'
            }
    except Exception as e:
        return {
            'success': False,
            'message': f'Connection test failed: {str(e)}',
            'test_method': 'Basic TCP'
        }

def test_ssh_connection_local(credentials: dict) -> dict:
    """Test SSH connection locally using paramiko"""
    if not PARAMIKO_AVAILABLE:
        # Fallback to basic connectivity test
        host = credentials.get('host')
        port = credentials.get('port', 22)
        basic_test = test_basic_connectivity(host, port)

        if basic_test.get('success'):
            return {
                'success': True,
                'message': f'Basic connectivity to {host}:{port} successful (SSH library not available for full test)',
                'test_method': 'Basic TCP (paramiko unavailable)',
                'connection_time': 'N/A'
            }
        else:
            return {
                'success': False,
                'message': basic_test.get('message', 'Connection failed'),
                'test_method': 'Basic TCP (paramiko unavailable)'
            }

    start_time = datetime.now()

    try:
        # Extract connection details
        host = credentials.get('host')
        port = credentials.get('port', 22)
        username = credentials.get('username')
        password = credentials.get('password')
        private_key = credentials.get('private_key')
        private_key_passphrase = credentials.get('private_key_passphrase')

        # Create SSH client
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        # Prepare authentication
        auth_kwargs = {
            'hostname': host,
            'port': int(port),
            'username': username,
            'timeout': 10
        }

        # Use private key if provided
        if private_key and private_key.strip():
            try:
                from io import StringIO
                key_file = StringIO(private_key)

                # Try different key types
                for key_class in [paramiko.RSAKey, paramiko.DSAKey, paramiko.ECDSAKey, paramiko.Ed25519Key]:
                    try:
                        key_file.seek(0)
                        pkey = key_class.from_private_key(key_file, password=private_key_passphrase)
                        auth_kwargs['pkey'] = pkey
                        break
                    except Exception:
                        continue
                else:
                    return {
                        'success': False,
                        'message': 'Invalid private key format',
                        'test_method': 'Local SSH'
                    }
            except Exception as e:
                return {
                    'success': False,
                    'message': f'Private key error: {str(e)}',
                    'test_method': 'Local SSH'
                }
        elif password:
            auth_kwargs['password'] = password
        else:
            return {
                'success': False,
                'message': 'No authentication method provided',
                'test_method': 'Local SSH'
            }

        # Attempt connection
        ssh.connect(**auth_kwargs)

        # Test with a simple command
        stdin, stdout, stderr = ssh.exec_command('echo "SSH connection test successful"')
        output = stdout.read().decode().strip()

        ssh.close()

        connection_time = (datetime.now() - start_time).total_seconds()

        return {
            'success': True,
            'message': 'SSH connection successful',
            'connection_time': f'{connection_time:.2f}s',
            'test_method': 'Local SSH',
            'test_output': output
        }

    except paramiko.AuthenticationException:
        return {
            'success': False,
            'message': 'Authentication failed - invalid credentials',
            'test_method': 'Local SSH'
        }
    except paramiko.SSHException as e:
        error_msg = str(e)
        if 'forcibly closed' in error_msg:
            return {
                'success': False,
                'message': 'SSH connection rejected by server - check server configuration, fail2ban, or rate limiting',
                'test_method': 'Local SSH',
                'details': f'SSH service detected but connection rejected: {error_msg}'
            }
        else:
            return {
                'success': False,
                'message': f'SSH connection error: {error_msg}',
                'test_method': 'Local SSH'
            }
    except socket.timeout:
        return {
            'success': False,
            'message': 'Connection timeout - host may be unreachable',
            'test_method': 'Local SSH'
        }
    except socket.gaierror:
        return {
            'success': False,
            'message': 'DNS resolution failed - invalid hostname',
            'test_method': 'Local SSH'
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'Connection failed: {str(e)}',
            'test_method': 'Local SSH'
        }

def test_xray_service_local(credentials: dict) -> dict:
    """Test Xray service locally via SSH"""
    if not PARAMIKO_AVAILABLE:
        # Fallback to basic connectivity test
        host = credentials.get('host')
        port = credentials.get('port', 22)
        basic_test = test_basic_connectivity(host, port)

        if basic_test.get('success'):
            return {
                'success': True,
                'message': f'Basic connectivity to {host}:{port} successful (SSH library not available for Xray service test)',
                'test_method': 'Basic TCP (paramiko unavailable)',
                'details': {
                    'config_exists': 'unknown',
                    'config_valid': 'unknown',
                    'service_exists': 'unknown',
                    'service_active': 'unknown'
                }
            }
        else:
            return {
                'success': False,
                'message': basic_test.get('message', 'Connection failed'),
                'test_method': 'Basic TCP (paramiko unavailable)'
            }

    # First test SSH connection
    ssh_result = test_ssh_connection_local(credentials)
    if not ssh_result.get('success'):
        return {
            'success': False,
            'message': f'SSH connection failed: {ssh_result.get("message")}',
            'test_method': 'Local SSH'
        }

    try:
        # Extract connection details
        host = credentials.get('host')
        port = credentials.get('port', 22)
        username = credentials.get('username')
        password = credentials.get('password')
        private_key = credentials.get('private_key')
        private_key_passphrase = credentials.get('private_key_passphrase')
        xray_config_path = credentials.get('xray_config_path', '/etc/xray/config.json')
        xray_service_name = credentials.get('xray_service_name', 'xray')

        # Create SSH client
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        # Connect (reuse connection logic from SSH test)
        auth_kwargs = {
            'hostname': host,
            'port': int(port),
            'username': username,
            'timeout': 10
        }

        if private_key and private_key.strip():
            from io import StringIO
            key_file = StringIO(private_key)
            for key_class in [paramiko.RSAKey, paramiko.DSAKey, paramiko.ECDSAKey, paramiko.Ed25519Key]:
                try:
                    key_file.seek(0)
                    pkey = key_class.from_private_key(key_file, password=private_key_passphrase)
                    auth_kwargs['pkey'] = pkey
                    break
                except Exception:
                    continue
        elif password:
            auth_kwargs['password'] = password

        ssh.connect(**auth_kwargs)

        # Test Xray configuration file
        stdin, stdout, stderr = ssh.exec_command(f'test -f {xray_config_path} && echo "exists" || echo "missing"')
        config_exists = stdout.read().decode().strip() == 'exists'

        # Test Xray service status
        stdin, stdout, stderr = ssh.exec_command(f'systemctl is-active {xray_service_name}')
        service_active = stdout.read().decode().strip() == 'active'

        # Test if service exists
        stdin, stdout, stderr = ssh.exec_command(f'systemctl list-unit-files | grep {xray_service_name}')
        service_exists = len(stdout.read().decode().strip()) > 0

        # Test config validity (if exists)
        config_valid = False
        if config_exists:
            stdin, stdout, stderr = ssh.exec_command(f'python3 -m json.tool {xray_config_path} > /dev/null 2>&1 && echo "valid" || echo "invalid"')
            config_valid = stdout.read().decode().strip() == 'valid'

        ssh.close()

        # Determine overall success
        success = config_exists and service_exists and service_active and config_valid

        details = {
            'config_exists': config_exists,
            'config_valid': config_valid,
            'service_exists': service_exists,
            'service_active': service_active,
            'config_path': xray_config_path,
            'service_name': xray_service_name
        }

        if success:
            message = 'Xray service is properly configured and running'
        else:
            issues = []
            if not config_exists:
                issues.append(f'Config file missing: {xray_config_path}')
            if not config_valid:
                issues.append('Config file is invalid JSON')
            if not service_exists:
                issues.append(f'Service not found: {xray_service_name}')
            if not service_active:
                issues.append(f'Service not active: {xray_service_name}')
            message = '; '.join(issues)

        return {
            'success': success,
            'message': message,
            'details': details,
            'test_method': 'Local SSH'
        }

    except Exception as e:
        return {
            'success': False,
            'message': f'Xray service test failed: {str(e)}',
            'test_method': 'Local SSH'
        }

def create_vpn_blueprint(plugin, api_service, config_service, redemption_service) -> Blueprint:
    """Create VPN blueprint with all routes"""
    # Specify template_folder to use plugin's templates
    template_folder = os.path.join(os.path.dirname(__file__), '..', 'templates')
    bp = Blueprint('vpn', __name__, template_folder=template_folder)

    # ========== Helper Functions ==========

    def handle_api_error(func):
        """Decorator to handle API errors"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in {func.__name__}: {e}")
                if request.is_json:
                    return jsonify({'error': str(e)}), 500
                flash(f'Error: {str(e)}', 'error')
                # Avoid redirect loop if the error occurred on the servers page itself
                try:
                    current_endpoint = request.endpoint
                except Exception:
                    current_endpoint = None
                fallback = request.referrer or url_for('vpn.servers')
                if current_endpoint == 'vpn.servers':
                    fallback = url_for('vpn.health_dashboard')
                return redirect(fallback)
        return wrapper

    def login_required(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'admin_logged_in' not in session:
                return redirect(url_for('admin.login'))
            return f(*args, **kwargs)
        return decorated_function

    # ========== Plugin Configuration Routes ==========

    @bp.route('/api/config', methods=['GET'])
    @login_required
    def get_plugin_config():
        """Get the VPN plugin's configuration"""
        try:
            return jsonify({
                'success': True,
                'config': plugin.config
            })
        except Exception as e:
            logger.error(f"Error getting plugin config: {e}")
            return jsonify({'success': False, 'message': str(e)}), 500

    @bp.route('/api/config', methods=['PUT'])
    @login_required
    def update_plugin_config():
        """Update the VPN plugin's configuration"""
        try:
            new_config = request.get_json()
            if not new_config:
                return jsonify({'success': False, 'message': 'Invalid configuration provided'}), 400

            # Update and save the configuration via the plugin manager
            success = plugin.plugin_manager.update_plugin_config(plugin.name, new_config)

            if success:
                # Reload the plugin's internal config
                plugin.config = new_config
                # Apply config immediately to API service (base_url, creds, timeout)
                try:
                    api_service.apply_config(new_config)
                except Exception as e:
                    logger.error(f"Failed to apply VPN API service config: {e}")
                return jsonify({'success': True, 'message': 'Configuration updated successfully'})
            else:
                return jsonify({'success': False, 'message': 'Failed to update configuration'}), 500
        except Exception as e:
            logger.error(f"Error updating plugin config: {e}")
            return jsonify({'success': False, 'message': str(e)}), 500

    # ========== Server Management Routes ==========

    @bp.route('/servers')
    @login_required
    @handle_api_error
    def servers():
        """Display VPN servers page"""
        # Fetch servers with a reasonable timeout to avoid premature failures
        servers_raw = api_service.get_servers(timeout=10)
        servers_data = []
        if isinstance(servers_raw, list):
            servers_data = servers_raw
        elif isinstance(servers_raw, dict):
            for key in ('servers', 'items', 'data', 'results'):
                value = servers_raw.get(key)
                if isinstance(value, list):
                    servers_data = value
                    break

        # Fallback to local servers file when API is slow/unavailable
        if not servers_data:
            servers_file = plugin.config.get('server_config', {}).get('servers_file', 'configs/services/vpn_servers.json')
            try:
                if os.path.exists(servers_file):
                    with open(servers_file, 'r', encoding='utf-8') as f:
                        file_servers = json.load(f)
                        if isinstance(file_servers, list):
                            servers_data = file_servers
            except Exception as fe:
                logger.warning(f"Failed to load servers from {servers_file}: {fe}")

        return render_template('vpn_servers.html',
                               servers=servers_data,
                               title='VPN Servers')

    @bp.route('/servers/create', methods=['GET', 'POST'])
    @login_required
    @handle_api_error
    def create_server():
        """Create new VPN server"""
        print("DEBUG: CREATE SERVER ROUTE CALLED!")  # Very obvious debug
        if request.method == 'POST':
            print("DEBUG: POST REQUEST RECEIVED!")  # Very obvious debug
            server_data = {
                'name': request.form.get('name'),
                'host': request.form.get('host'),
                'port': int(request.form.get('port', 22)),
                'username': request.form.get('username'),
                'password': request.form.get('password'),
                'private_key': request.form.get('private_key'),
                'private_key_passphrase': request.form.get('private_key_passphrase'),
                'description': request.form.get('description'),
                'xray_config_path': request.form.get('xray_config_path', '/etc/xray/config.json'),
                'xray_service_name': request.form.get('xray_service_name', 'xray'),
                'is_active': request.form.get('is_active') == 'on',
                'tags': request.form.get('tags', '').split(',') if request.form.get('tags') else []
            }

            # Remove None values
            server_data = {k: v for k, v in server_data.items() if v is not None and v != ''}

            # Skip credential testing for now due to API issues - proceed directly to server creation
            # TODO: Re-enable credential testing once the API endpoint is fixed
            should_proceed = True
            credential_test_warning = "Credential testing skipped due to API issues"

            logger.warning("Skipping credential test due to API issues, proceeding directly with server creation")
            print("DEBUG: Skipping credential test, proceeding with server creation")  # Debug print

            if should_proceed:
                # Show credential test warning if there was one
                if credential_test_warning:
                    flash(f'Warning: {credential_test_warning}', 'warning')

                logger.info(f"Creating server with data: {server_data}")
                result = api_service.create_server(server_data)
                logger.info(f"Server creation result: {result}")
                if result:
                    server_id = result.get('id')
                    if server_id:
                        check_result = api_service.check_server_config_format(server_id)
                        if check_result and check_result.get('is_valid', False):
                            flash(f'Server {server_data["name"]} created and configuration validated successfully!', 'success')
                        else:
                            msg = f'Server {server_data["name"]} created but configuration validation failed.'
                            if check_result:
                                msg += f' Errors: {", ".join(check_result.get("validation_errors", []))}.'
                                msg += f' Health status updated to {check_result.get("health_status")}.'
                            flash(msg, 'warning')
                    else:
                        flash(f'Server {server_data["name"]} created successfully but could not retrieve ID for validation.', 'warning')
                    return redirect(url_for('vpn.servers'))
                else:
                    flash('Failed to create server - API returned no result', 'error')

        return render_template('vpn_server_form.html',
                             title='Create VPN Server',
                             action='create')

    @bp.route('/servers/<int:server_id>/edit', methods=['GET', 'POST'])
    @login_required
    @handle_api_error
    def edit_server(server_id: int):
        """Edit VPN server"""
        server = api_service.get_server(server_id)
        if not server:
            flash('Server not found', 'error')
            return redirect(url_for('vpn.servers'))

        if request.method == 'POST':
            update_data = {
                'name': request.form.get('name'),
                'host': request.form.get('host'),
                'port': int(request.form.get('port', 22)) if request.form.get('port') else None,
                'username': request.form.get('username'),
                'password': request.form.get('password') if request.form.get('password') else None,
                'private_key': request.form.get('private_key') if request.form.get('private_key') else None,
                'private_key_passphrase': request.form.get('private_key_passphrase') if request.form.get('private_key_passphrase') else None,
                'description': request.form.get('description'),
                'xray_config_path': request.form.get('xray_config_path'),
                'xray_service_name': request.form.get('xray_service_name'),
                'is_active': request.form.get('is_active') == 'on',
                'tags': request.form.get('tags', '').split(',') if request.form.get('tags') else []
            }

            # Remove None values
            update_data = {k: v for k, v in update_data.items() if v is not None}

            result = api_service.update_server(server_id, update_data)
            if result:
                flash('Server updated successfully!', 'success')
                return redirect(url_for('vpn.servers'))
            else:
                flash('Failed to update server', 'error')

        return render_template('vpn_server_form.html',
                             server=server,
                             title='Edit VPN Server',
                             action='edit')

    @bp.route('/servers/<int:server_id>/delete', methods=['POST'])
    @login_required
    @handle_api_error
    def delete_server(server_id: int):
        """Delete VPN server"""
        if api_service.delete_server(server_id):
            flash('Server deleted successfully!', 'success')
        else:
            flash('Failed to delete server', 'error')
        return redirect(url_for('vpn.servers'))

    @bp.route('/servers/<int:server_id>/test-connection', methods=['POST'])
    @login_required
    @handle_api_error
    def test_server_connection(server_id: int):
        """Test server connection"""
        try:
            result = api_service.test_server_connection(server_id)

            # Check for JSON request by Accept header or Content-Type
            is_json_request = (request.is_json or
                             'application/json' in request.headers.get('Accept', '') or
                             'application/json' in request.headers.get('Content-Type', ''))

            if is_json_request:
                return jsonify(result or {'success': False, 'message': 'Connection test failed'})

            if result and result.get('success'):
                flash('Connection test successful!', 'success')
            else:
                error_msg = result.get('message') if result else 'Unknown error'
                flash(f'Connection test failed: {error_msg}', 'error')
                logger.error(f'Server {server_id} connection test failed: {error_msg}')

        except Exception as e:
            error_msg = f'Connection test error: {str(e)}'
            logger.error(f'Server {server_id} connection test exception: {e}')

            # Check for JSON request by Accept header or Content-Type
            is_json_request = (request.is_json or
                             'application/json' in request.headers.get('Accept', '') or
                             'application/json' in request.headers.get('Content-Type', ''))

            if is_json_request:
                return jsonify({'success': False, 'message': error_msg}), 500

            flash(error_msg, 'error')

        return redirect(url_for('vpn.servers'))

    @bp.route('/servers/<int:server_id>/restart-xray', methods=['POST'])
    @login_required
    @handle_api_error
    def restart_xray(server_id: int):
        """Restart Xray service"""
        if api_service.restart_xray_service(server_id):
            flash('Xray service restarted successfully!', 'success')
        else:
            flash('Failed to restart Xray service', 'error')
        return redirect(url_for('vpn.servers'))

    @bp.route('/servers/<int:server_id>/service-status')
    @login_required
    @handle_api_error
    def service_status(server_id: int):
        """Get service status"""
        status = api_service.get_service_status(server_id)
        if request.is_json:
            return jsonify(status or {'error': 'Failed to get service status'})
        return render_template('vpn_service_status.html',
                             server_id=server_id,
                             status=status)

    @bp.route('/servers/<int:server_id>/clients')
    @login_required
    @handle_api_error
    def manage_server_clients(server_id: int):
        """Manage clients for a specific server"""
        try:
            # Get detailed server and client information
            server_data = api_service.get_server_clients_detailed(server_id)

            if not server_data:
                flash('Failed to load server client data', 'error')
                return redirect(url_for('vpn.servers'))

            server = server_data.get('server', {})
            clients = server_data.get('clients', [])
            client_stats = api_service.get_server_client_stats(server_id)

            # Get pagination parameters
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 50, type=int)
            search = request.args.get('search', '', type=str)
            status_filter = request.args.get('status', 'all', type=str)

            # Filter clients based on search and status
            filtered_clients = clients
            if search:
                filtered_clients = [
                    c for c in filtered_clients
                    if search.lower() in c.get('email', '').lower() or
                       search.lower() in c.get('shopee_username', '').lower()
                ]

            if status_filter == 'active':
                filtered_clients = [c for c in filtered_clients if c.get('is_active', False)]
            elif status_filter == 'expired':
                filtered_clients = [c for c in filtered_clients if c.get('is_expired', False)]
            elif status_filter == 'expiring_soon':
                filtered_clients = [c for c in filtered_clients if c.get('days_until_expiry', 999) <= 7 and not c.get('is_expired', False)]

            # Simple pagination
            total_clients = len(filtered_clients)
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            paginated_clients = filtered_clients[start_idx:end_idx]

            # Pagination info
            has_prev = page > 1
            has_next = end_idx < total_clients

            return render_template('vpn_server_clients.html',
                                 server=server,
                                 clients=paginated_clients,
                                 client_stats=client_stats or {},
                                 total_clients=total_clients,
                                 page=page,
                                 per_page=per_page,
                                 has_prev=has_prev,
                                 has_next=has_next,
                                 search=search,
                                 status_filter=status_filter)

        except Exception as e:
            logger.error(f"Error in manage_server_clients: {e}")
            flash(f'Error loading server clients: {str(e)}', 'error')
            return redirect(url_for('vpn.servers'))

    # ========== Client Management Routes ==========

    @bp.route('/clients')
    @login_required
    @handle_api_error
    def clients():
        """Display VPN clients page"""
        # Auto-sync clients if requested
        auto_sync = request.args.get('auto_sync', 'false').lower() == 'true'
        if auto_sync:
            try:
                # Sync all servers clients
                sync_result = api_service.sync_all_servers_clients()
                if sync_result and sync_result.get('success'):
                    flash('Clients synchronized successfully!', 'success')
                else:
                    flash('Failed to synchronize clients', 'warning')
            except Exception as e:
                logger.error(f"Auto-sync failed: {e}")
                flash(f'Auto-sync failed: {str(e)}', 'warning')

        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        server_id = request.args.get('server_id')
        if not server_id or not server_id.isdigit():
            server_id = None
        else:
            server_id = int(server_id)

        is_active = request.args.get('is_active')
        if is_active and is_active.strip():  # Check for non-empty string
            is_active = is_active.lower() == 'true'
        else:
            is_active = None  # No filter

        is_expired = request.args.get('is_expired')
        if is_expired and is_expired.strip():  # Check for non-empty string
            is_expired = is_expired.lower() == 'true'
        else:
            is_expired = None  # No filter
        search = request.args.get('search', '')

        # Calculate skip
        skip = (page - 1) * per_page

        # Get clients
        result = api_service.get_clients(
            skip=skip,
            limit=per_page,
            server_id=server_id,
            is_active=is_active,
            is_expired=is_expired,
            search=search
        )

        if result:
            clients_data = result.get('clients', [])
            total = result.get('total', 0)
            total_pages = (total + per_page - 1) // per_page
        else:
            clients_data = []
            total = 0
            total_pages = 0

        # Get servers for filter dropdown
        servers = api_service.get_servers() or []

        return render_template('vpn_clients.html',
                              clients=clients_data,
                              servers=servers,
                              page=page,
                              per_page=per_page,
                              total=total,
                              total_pages=total_pages,
                              server_id=server_id,
                              is_active=is_active,
                              is_expired=is_expired,
                              search=search,
                              min=min,  # Explicitly pass the min function
                              title='VPN Clients')

    @bp.route('/clients/create', methods=['GET', 'POST'])
    @login_required
    @handle_api_error
    def create_client():
        """Create new VPN client"""
        servers = api_service.get_servers() or []

        if request.method == 'POST':
            client_data = {
                'email': request.form.get('email'),
                'server_id': int(request.form.get('server_id')),
                'expired_date': request.form.get('expired_date'),
                'shopee_username': request.form.get('shopee_username'),
                'description': request.form.get('description'),
                'notes': request.form.get('notes'),
                'client_id': request.form.get('client_id')  # Optional custom client ID
            }

            # Remove empty values
            client_data = {k: v for k, v in client_data.items() if v}

            result = api_service.create_client(client_data)
            if result:
                flash(f'Client {client_data["email"]} created successfully!', 'success')

                # Auto-restart Xray service on the target server
                server_id = client_data.get('server_id')
                if server_id:
                    auto_restart_xray_service(api_service, server_id, f"adding client {client_data['email']}")

                # Redirect based on context
                redirect_to = request.args.get('redirect_to', 'vpn.clients')
                if redirect_to == 'server_clients' and server_id:
                    return redirect(url_for('vpn.manage_server_clients', server_id=server_id))
                else:
                    return redirect(url_for('vpn.clients'))
            else:
                flash('Failed to create client', 'error')

        return render_template('vpn_client_form.html',
                             servers=servers,
                             title='Create VPN Client',
                             action='create')

    @bp.route('/clients/<int:client_id>/edit', methods=['GET', 'POST'])
    @login_required
    @handle_api_error
    def edit_client(client_id: int):
        """Edit VPN client"""
        client = api_service.get_client(client_id)
        if not client:
            flash('Client not found', 'error')
            return redirect(url_for('vpn.clients'))

        if request.method == 'POST':
            update_data = {
                'email': request.form.get('email'),
                'expired_date': request.form.get('expired_date'),
                'shopee_username': request.form.get('shopee_username'),
                'description': request.form.get('description'),
                'notes': request.form.get('notes'),
                'is_active': request.form.get('is_active') == 'on'
            }

            # Remove None values
            update_data = {k: v for k, v in update_data.items() if v is not None}

            result = api_service.update_client(client_id, update_data)
            if result:
                flash('Client updated successfully!', 'success')
                return redirect(url_for('vpn.clients'))
            else:
                flash('Failed to update client', 'error')

        return render_template('vpn_client_form.html',
                             client=client,
                             title='Edit VPN Client',
                             action='edit')

    @bp.route('/clients/<int:client_id>/delete', methods=['POST'])
    @login_required
    @handle_api_error
    def delete_client(client_id: int):
        """Delete VPN client"""
        # Get client info before deletion to know which server to restart
        client = api_service.get_client(client_id)

        if api_service.delete_client(client_id):
            flash('Client deleted successfully!', 'success')

            # Auto-restart Xray service on the client's server
            if client and client.get('server_id'):
                server_id = client['server_id']
                client_email = client.get('email', f'client {client_id}')
                auto_restart_xray_service(api_service, server_id, f"deleting client {client_email}")
        else:
            flash('Failed to delete client', 'error')
        return redirect(url_for('vpn.clients'))

    @bp.route('/clients/<int:client_id>/extend', methods=['POST'])
    @login_required
    @handle_api_error
    def extend_client(client_id: int):
        """Extend client expiry"""
        days = request.form.get('days', 30, type=int)
        if api_service.extend_client_expiry(client_id, days):
            flash(f'Client expiry extended by {days} days!', 'success')
        else:
            flash('Failed to extend client expiry', 'error')
        return redirect(url_for('vpn.clients'))

    @bp.route('/clients/bulk-create', methods=['GET', 'POST'])
    @login_required
    @handle_api_error
    def bulk_create_clients():
        """Bulk create VPN clients"""
        servers = api_service.get_servers() or []

        if request.method == 'POST':
            server_id = int(request.form.get('server_id'))
            clients_text = request.form.get('clients_data', '')

            # Parse clients data (expecting CSV format: email,expired_date,shopee_username,description)
            clients = []
            for line in clients_text.strip().split('\n'):
                if line.strip():
                    parts = line.strip().split(',')
                    if len(parts) >= 2:
                        client = {
                            'email': parts[0].strip(),
                            'expired_date': parts[1].strip()
                        }
                        if len(parts) > 2 and parts[2].strip():
                            client['shopee_username'] = parts[2].strip()
                        if len(parts) > 3 and parts[3].strip():
                            client['description'] = parts[3].strip()
                        clients.append(client)

            if clients:
                result = api_service.create_clients_bulk(server_id, clients)
                if result:
                    created = result.get('created_count', 0)
                    failed = result.get('failed_count', 0)
                    flash(f'Bulk creation completed: {created} created, {failed} failed', 'success')
                    if result.get('errors'):
                        for error in result['errors']:
                            flash(error, 'warning')

                    # Auto-restart Xray service after successful bulk creation
                    if created > 0:
                        auto_restart_xray_service(api_service, server_id, f"bulk adding {created} clients")

                    # Redirect based on context
                    redirect_to = request.args.get('redirect_to', 'vpn.clients')
                    if redirect_to == 'server_clients':
                        return redirect(url_for('vpn.manage_server_clients', server_id=server_id))
                    else:
                        return redirect(url_for('vpn.clients'))
                else:
                    flash('Bulk creation failed', 'error')
            else:
                flash('No valid client data provided', 'error')

        return render_template('vpn_bulk_create.html',
                             servers=servers,
                             title='Bulk Create VPN Clients')

    # ========== Client Sync Management Routes ==========

    @bp.route('/clients/sync')
    @login_required
    @handle_api_error
    def client_sync():
        """Display client sync management page"""
        servers = api_service.get_servers() or []

        # Get sync comparison for each server
        sync_status = {}
        for server in servers:
            try:
                comparison = api_service.compare_server_clients(server['id'])
                if comparison:
                    sync_status[server['id']] = {
                        'in_sync': comparison.get('in_sync', False),
                        'only_in_config': len(comparison.get('only_in_config', [])),
                        'only_in_database': len(comparison.get('only_in_database', [])),
                        'mismatched': len(comparison.get('mismatched', []))
                    }
                else:
                    sync_status[server['id']] = {
                        'in_sync': False,
                        'only_in_config': 0,
                        'only_in_database': 0,
                        'mismatched': 0
                    }
            except Exception as e:
                logger.error(f"Failed to get sync status for server {server['id']}: {e}")
                sync_status[server['id']] = {
                    'in_sync': False,
                    'only_in_config': 0,
                    'only_in_database': 0,
                    'mismatched': 0
                }

        return render_template('vpn_client_sync.html',
                             servers=servers,
                             sync_status=sync_status,
                             title='Client Sync Management')

    @bp.route('/clients/sync/server/<int:server_id>', methods=['POST'])
    @login_required
    @handle_api_error
    def sync_server_clients(server_id: int):
        """Sync clients for a specific server"""
        try:
            result = api_service.sync_server_clients(server_id)
            if result and result.get('success'):
                flash(f'Server {server_id} clients synchronized successfully!', 'success')
            else:
                flash(f'Failed to sync server {server_id} clients', 'error')
        except Exception as e:
            logger.error(f"Failed to sync server {server_id}: {e}")
            flash(f'Sync failed: {str(e)}', 'error')

        return redirect(url_for('vpn.client_sync'))

    @bp.route('/clients/sync/all', methods=['POST'])
    @login_required
    @handle_api_error
    def sync_all_clients():
        """Sync clients for all servers"""
        try:
            result = api_service.sync_all_servers_clients()
            if result and result.get('success'):
                flash('All servers synchronized successfully!', 'success')
            else:
                flash('Failed to sync all servers', 'error')
        except Exception as e:
            logger.error(f"Failed to sync all servers: {e}")
            flash(f'Sync failed: {str(e)}', 'error')

        return redirect(url_for('vpn.client_sync'))

    @bp.route('/clients/cleanup/<int:server_id>', methods=['POST'])
    @login_required
    @handle_api_error
    def cleanup_orphaned_clients(server_id: int):
        """Cleanup orphaned clients for a specific server"""
        try:
            result = api_service.cleanup_orphaned_clients(server_id)
            if result and result.get('success'):
                flash(f'Server {server_id} orphaned clients cleaned up successfully!', 'success')
            else:
                flash(f'Failed to cleanup server {server_id} orphaned clients', 'error')
        except Exception as e:
            logger.error(f"Failed to cleanup server {server_id}: {e}")
            flash(f'Cleanup failed: {str(e)}', 'error')

        return redirect(url_for('vpn.client_sync'))

    # ========== Configuration Management Routes ==========

    @bp.route('/configurations')
    @login_required
    @handle_api_error
    def configurations():
        """Display configurations overview"""
        config_overview = api_service.get_configurations()
        return render_template('vpn_configurations.html',
                             overview=config_overview,
                             title='VPN Configurations')

    @bp.route('/configurations/server/<int:server_id>')
    @login_required
    @handle_api_error
    def server_configuration(server_id: int):
        """Display server configuration"""
        config = api_service.get_server_configuration(server_id)
        validation = api_service.validate_server_config(server_id)

        return render_template('vpn_server_config.html',
                             server_id=server_id,
                             config=config,
                             validation=validation,
                             title='Server Configuration')

    @bp.route('/configurations/server/<int:server_id>/edit', methods=['GET', 'POST'])
    @login_required
    @handle_api_error
    def edit_server_configuration(server_id: int):
        """Edit server configuration"""
        if request.method == 'POST':
            config_json = request.form.get('config')
            create_backup = request.form.get('create_backup') == 'on'

            try:
                import json
                config = json.loads(config_json)
                result = api_service.update_server_configuration(server_id, config, create_backup)
                if result and result.get('success'):
                    flash('Configuration updated successfully!', 'success')
                    return redirect(url_for('vpn.server_configuration', server_id=server_id))
                else:
                    flash(f'Failed to update configuration: {result.get("message") if result else "Unknown error"}', 'error')
            except json.JSONDecodeError:
                flash('Invalid JSON format', 'error')

        config = api_service.get_server_configuration(server_id)
        return render_template('vpn_config_edit.html',
                             server_id=server_id,
                             config=config,
                             title='Edit Server Configuration')

    @bp.route('/configurations/backup', methods=['GET', 'POST'])
    @login_required
    @handle_api_error
    def backup_configurations():
        """Backup configurations"""
        servers = api_service.get_servers() or []

        if request.method == 'POST':
            server_ids = request.form.getlist('server_ids')
            description = request.form.get('description')

            # Convert to integers
            server_ids = [int(sid) for sid in server_ids] if server_ids else None

            result = api_service.backup_configurations(server_ids, description)
            if result and result.get('success'):
                flash(f'Backup completed: {result.get("total_backups")} successful, {result.get("failed_backups")} failed', 'success')
            else:
                flash('Backup failed', 'error')

        # Get existing backups
        backups = api_service.list_config_backups()

        return render_template('vpn_backups.html',
                             servers=servers,
                             backups=backups,
                             title='Configuration Backups')

    @bp.route('/configurations/restore', methods=['POST'])
    @login_required
    @handle_api_error
    def restore_configuration():
        """Restore configuration from backup"""
        server_id = int(request.form.get('server_id'))
        backup_path = request.form.get('backup_path')
        create_backup = request.form.get('create_backup') == 'on'

        result = api_service.restore_configuration(server_id, backup_path, create_backup)
        if result and result.get('success'):
            flash('Configuration restored successfully!', 'success')
        else:
            flash(f'Restore failed: {result.get("message") if result else "Unknown error"}', 'error')

        return redirect(url_for('vpn.backup_configurations'))

    @bp.route('/configurations/sync', methods=['GET', 'POST'])
    @login_required
    @handle_api_error
    def sync_configurations():
        """Sync configurations"""
        servers = api_service.get_servers() or []

        if request.method == 'POST':
            server_ids = request.form.getlist('server_ids')
            sync_direction = request.form.get('sync_direction', 'config_to_db')
            remove_orphaned = request.form.get('remove_orphaned') == 'on'

            # Convert to integers
            server_ids = [int(sid) for sid in server_ids] if server_ids else None

            result = api_service.sync_configurations(server_ids, sync_direction, remove_orphaned)
            if result and result.get('success'):
                flash(f'Sync completed: {result.get("total_synced_clients")} synced, {result.get("total_removed_clients")} removed', 'success')
            else:
                flash('Sync failed', 'error')

        return render_template('vpn_sync.html',
                             servers=servers,
                             title='Sync Configurations')

    # ========== Health Monitoring Routes ==========

    @bp.route('/health')
    @login_required
    @handle_api_error
    def health_dashboard():
        """Display health dashboard"""
        health_data = api_service.get_health_dashboard()
        return render_template('vpn_health.html',
                             health=health_data,
                             title='VPN Health Dashboard')

    @bp.route('/health/refresh', methods=['POST'])
    @login_required
    @handle_api_error
    def refresh_health():
        """Refresh health status"""
        server_id = request.form.get('server_id', type=int)

        if server_id:
            result = api_service.refresh_server_health(server_id)
            # Transform single server health data for JSON response
            if result and request.is_json:
                transformed_result = {
                    'is_healthy': result.get('status') == 'healthy',
                    'status': result.get('status', 'unknown'),
                    'last_check': result.get('last_check'),
                    'response_time': result.get('response_time'),
                    'connection_status': result.get('connection_status', False),
                    'service_status': result.get('service_status', False),
                    'client_stats': {
                        'total': result.get('client_count', 0),
                        'active': result.get('active_clients', 0),
                        'expired': result.get('client_count', 0) - result.get('active_clients', 0)
                    }
                }
                return jsonify(transformed_result)
        else:
            result = api_service.refresh_all_servers_health()

        if request.is_json:
            return jsonify(result or {'error': 'Failed to refresh health'})

        if result:
            flash('Health status refreshed!', 'success')
        else:
            flash('Failed to refresh health status', 'error')

        return redirect(url_for('vpn.health_dashboard'))

    # ========== WebSocket Monitoring Routes ==========

    @bp.route('/websocket')
    @login_required
    @handle_api_error
    def websocket_monitoring():
        """Display WebSocket monitoring page"""
        ws_stats = api_service.get_websocket_stats()
        return render_template('vpn_websocket.html',
                             ws_stats=ws_stats,
                             title='WebSocket Monitoring')

    # ========== Background Tasks Routes ==========

    @bp.route('/tasks')
    @login_required
    @handle_api_error
    def background_tasks():
        """Display background tasks page"""
        tasks = api_service.get_tasks()
        return render_template('vpn_tasks.html',
                             tasks=tasks,
                             title='Background Tasks')

    @bp.route('/tasks/enqueue', methods=['POST'])
    @login_required
    @handle_api_error
    def enqueue_task():
        """Enqueue a background task"""
        result = api_service.enqueue_background_task()
        if result:
            flash('Task enqueued successfully!', 'success')
        else:
            flash('Failed to enqueue task', 'error')
        return redirect(url_for('vpn.background_tasks'))

    @bp.route('/tasks/<int:task_id>')
    @login_required
    @handle_api_error
    def get_task(task_id: int):
        """Get task details"""
        task = api_service.get_task(task_id)
        if request.is_json:
            return jsonify(task or {'error': 'Task not found'})
        return render_template('vpn_task_detail.html',
                             task=task,
                             title=f'Task {task_id} Details')

    # ========== Settings Route ==========

    @bp.route('/settings')
    @login_required
    @handle_api_error
    def settings():
        """Display VPN settings page"""
        return render_template('vpn_settings.html',
                             config=config_service.config,
                             title='VPN Settings')

    # ========== API Endpoints ==========

    @bp.route('/api/get_config', methods=['GET'])
    @handle_api_error
    def api_get_config():
        """API endpoint to get VPN plugin configuration"""
        try:
            config_data = {
                'enabled': config_service.config.get('enabled', True),
                'vpn_api': config_service.config.get('vpn_api', {
                    'base_url': 'https://blueblue.api.limjianhui.com',
                    'username': 'admin',
                    'password': 'admin123',
                    'timeout': 30
                }),
                'client_config': config_service.config.get('client_config', {
                    'default_traffic_gb': 100,
                    'default_expiry_days': 30
                }),
                'redemption': config_service.config.get('redemption', {
                    'enabled': True,
                    'default_expiry_days': 30
                }),
                'server_config': config_service.config.get('server_config', {
                    'servers_file': 'configs/services/vpn_servers.json',
                    'connection_timeout': 30
                }),
                'config_templates': config_service.config.get('config_templates', {
                    'templates_file': 'configs/services/config_templates.json'
                })
            }
            return jsonify({
                'success': True,
                'config': config_data
            })
        except Exception as e:
            logger.error(f"Error getting VPN config: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @bp.route('/api/save_config', methods=['POST'])
    @handle_api_error
    def api_save_config():
        """API endpoint to save VPN plugin configuration"""
        try:
            config_data = request.get_json()
            if not config_data:
                return jsonify({
                    'success': False,
                    'error': 'No configuration data provided'
                }), 400

            # Update the configuration
            config_service.config.update(config_data)

            # Save the configuration (if config_service has a save method)
            if hasattr(config_service, 'save_config'):
                config_service.save_config()

            return jsonify({
                'success': True,
                'message': 'Configuration saved successfully'
            })
        except Exception as e:
            logger.error(f"Error saving VPN config: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @bp.route('/api/test_config', methods=['POST'])
    @handle_api_error
    def api_test_config():
        """API endpoint to test VPN configuration"""
        try:
            config_data = request.get_json()
            if not config_data:
                return jsonify({
                    'success': False,
                    'message': 'No configuration data provided'
                }), 400

            # Test the configuration using the API service
            test_result = api_service.test_connection()

            return jsonify({
                'success': test_result.get('success', False),
                'message': test_result.get('message', 'Configuration test completed')
            })
        except Exception as e:
            logger.error(f"Error testing VPN config: {e}")
            return jsonify({
                'success': False,
                'message': f'Error testing configuration: {str(e)}'
            }), 500

    @bp.route('/api/test-connection', methods=['POST'])
    @handle_api_error
    def api_test_connection():
        """API endpoint to test VPN API connection"""
        try:
            result = api_service.test_connection()
            return jsonify(result)
        except Exception as e:
            logger.error(f'API connection test failed: {e}')
            return jsonify({
                'success': False,
                'message': f'Connection test failed: {str(e)}'
            }), 500

    @bp.route('/api/servers', methods=['GET'])
    @handle_api_error
    def api_get_servers():
        """API endpoint to get servers"""
        servers = api_service.get_servers()
        return jsonify(servers or [])

    @bp.route('/api/clients', methods=['GET'])
    @handle_api_error
    def api_get_clients():
        """API endpoint to get clients"""
        server_id = request.args.get('server_id', type=int)
        search = request.args.get('search', '')

        result = api_service.get_clients(
            server_id=server_id,
            search=search,
            limit=1000  # Maximum allowed by API
        )

        return jsonify(result or {'clients': [], 'total': 0})

    @bp.route('/api/health/server/<int:server_id>', methods=['GET'])
    @handle_api_error
    def api_server_health(server_id: int):
        """API endpoint to get server health"""
        health = api_service.get_server_health(server_id)
        if health:
            # Get client information for this server
            clients_info = api_service.get_clients(server_id=server_id, limit=1000)

            total_clients = 0
            active_clients = 0

            if clients_info and 'clients' in clients_info:
                all_db_clients = clients_info['clients']
                total_clients = len(all_db_clients)
                active_clients = sum(1 for client in all_db_clients if client.get('is_active'))

            expired_clients = total_clients - active_clients

            # Transform health data to match template expectations
            transformed_health = {
                'is_healthy': health.get('status') == 'healthy',
                'status': health.get('status', 'unknown'),
                'last_check': health.get('last_check'),
                'response_time': health.get('response_time'),
                'connection_status': health.get('connection_status', False),
                'service_status': health.get('service_status', False),
                'client_stats': {
                    'total': total_clients,
                    'active': active_clients,
                    'expired': expired_clients
                },
                'issues': health.get('issues', []),
                'recommendations': health.get('recommendations', [])
            }

            return jsonify({
                'success': True,
                'health': transformed_health
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to get health status'
            })

    @bp.route('/api/sync/compare/<int:server_id>', methods=['GET'])
    @handle_api_error
    def api_compare_sync(server_id: int):
        """API endpoint to compare server sync status"""
        comparison = api_service.compare_server_clients(server_id)
        return jsonify(comparison or {'error': 'Failed to compare'})

    @bp.route('/api/sync/server/<int:server_id>', methods=['POST'])
    @handle_api_error
    def api_sync_server_clients(server_id: int):
        """API endpoint to sync clients from server to database"""
        result = api_service.sync_server_clients(server_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to sync server clients'}), 500

    @bp.route('/api/sync/all', methods=['POST'])
    @handle_api_error
    def api_sync_all_servers():
        """API endpoint to sync all servers clients"""
        result = api_service.sync_all_servers_clients()
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to sync all servers'}), 500

    @bp.route('/api/sync/analyze/<int:server_id>', methods=['GET'])
    @handle_api_error
    def api_analyze_invalid_clients(server_id: int):
        """API endpoint to analyze invalid clients"""
        result = api_service.analyze_invalid_clients(server_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to analyze invalid clients'}), 500

    @bp.route('/api/sync/cleanup/<int:server_id>', methods=['POST'])
    @handle_api_error
    def api_cleanup_orphaned_clients(server_id: int):
        """API endpoint to cleanup orphaned clients"""
        result = api_service.cleanup_orphaned_clients(server_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to cleanup orphaned clients'}), 500

    # ========== Client API Endpoints ==========

    @bp.route('/api/clients/create', methods=['POST'])
    @handle_api_error
    def api_create_client():
        """API endpoint to create a new client"""
        client_data = request.get_json()
        if not client_data:
            return jsonify({'error': 'No client data provided'}), 400

        result = api_service.create_client(client_data)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to create client'}), 500

    @bp.route('/api/clients/<int:client_id>', methods=['GET'])
    @handle_api_error
    def api_get_client(client_id: int):
        """API endpoint to get client by ID"""
        result = api_service.get_client(client_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Client not found'}), 404

    @bp.route('/api/clients/<int:client_id>', methods=['PUT'])
    @handle_api_error
    def api_update_client(client_id: int):
        """API endpoint to update client"""
        client_data = request.get_json()
        if not client_data:
            return jsonify({'error': 'No client data provided'}), 400

        result = api_service.update_client(client_id, client_data)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to update client'}), 500

    @bp.route('/api/clients/<int:client_id>', methods=['DELETE'])
    @handle_api_error
    def api_delete_client(client_id: int):
        """API endpoint to delete client"""
        if api_service.delete_client(client_id):
            return jsonify({'message': 'Client deleted successfully'})
        return jsonify({'error': 'Failed to delete client'}), 500

    @bp.route('/api/clients/by-email/<email>', methods=['GET'])
    @handle_api_error
    def api_get_client_by_email(email: str):
        """API endpoint to get client by email"""
        server_id = request.args.get('server_id', type=int)
        result = api_service.get_client_by_email(email, server_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Client not found'}), 404

    @bp.route('/api/clients/bulk', methods=['POST'])
    @handle_api_error
    def api_create_clients_bulk():
        """API endpoint to create clients in bulk"""
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        server_id = data.get('server_id')
        clients = data.get('clients', [])

        if not server_id or not clients:
            return jsonify({'error': 'server_id and clients are required'}), 400

        result = api_service.create_clients_bulk(server_id, clients)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to create clients in bulk'}), 500

    @bp.route('/api/clients/<int:client_id>/extend', methods=['POST'])
    @handle_api_error
    def api_extend_client_expiry(client_id: int):
        """API endpoint to extend client expiry"""
        days = request.args.get('days', type=int)
        if not days:
            return jsonify({'error': 'days parameter is required'}), 400

        if api_service.extend_client_expiry(client_id, days):
            return jsonify({'message': f'Client expiry extended by {days} days'})
        return jsonify({'error': 'Failed to extend client expiry'}), 500

    @bp.route('/api/clients/server/<int:server_id>/expiry', methods=['GET'])
    @handle_api_error
    def api_get_server_client_expiry(server_id: int):
        """API endpoint to get server client expiry information"""
        result = api_service.get_server_client_expiry(server_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to get client expiry info'}), 500

    # ========== Configuration API Endpoints ==========

    @bp.route('/api/config', methods=['GET'])
    @handle_api_error
    def api_get_configurations():
        """API endpoint to get configurations overview"""
        result = api_service.get_configurations()
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to get configurations'}), 500

    @bp.route('/api/config/server/<int:server_id>', methods=['GET'])
    @handle_api_error
    def api_get_server_configuration(server_id: int):
        """API endpoint to get server configuration"""
        result = api_service.get_server_configuration(server_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to get server configuration'}), 500

    @bp.route('/api/config/server/<int:server_id>', methods=['PUT'])
    @handle_api_error
    def api_update_server_configuration(server_id: int):
        """API endpoint to update server configuration"""
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No configuration data provided'}), 400

        config = data.get('config')
        create_backup = data.get('create_backup', True)

        if not config:
            return jsonify({'error': 'config field is required'}), 400

        result = api_service.update_server_configuration(server_id, config, create_backup)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to update server configuration'}), 500

    @bp.route('/api/config/backup', methods=['POST'])
    @handle_api_error
    def api_backup_configurations():
        """API endpoint to backup configurations"""
        data = request.get_json()
        server_ids = data.get('server_ids') if data else None
        description = data.get('description') if data else None

        result = api_service.backup_configurations(server_ids, description)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to backup configurations'}), 500

    @bp.route('/api/config/restore', methods=['POST'])
    @handle_api_error
    def api_restore_configuration():
        """API endpoint to restore configuration"""
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        server_id = data.get('server_id')
        backup_path = data.get('backup_path')
        create_backup_before_restore = data.get('create_backup_before_restore', True)

        if not server_id or not backup_path:
            return jsonify({'error': 'server_id and backup_path are required'}), 400

        result = api_service.restore_configuration(server_id, backup_path, create_backup_before_restore)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to restore configuration'}), 500

    @bp.route('/api/config/validate/server/<int:server_id>', methods=['GET'])
    @handle_api_error
    def api_validate_server_config(server_id: int):
        """API endpoint to validate server configuration"""
        result = api_service.validate_server_config(server_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to validate server configuration'}), 500

    @bp.route('/api/config/backups', methods=['GET'])
    @handle_api_error
    def api_list_config_backups():
        """API endpoint to list configuration backups"""
        server_id = request.args.get('server_id', type=int)
        result = api_service.list_config_backups(server_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to list configuration backups'}), 500

    @bp.route('/api/config/compare/server/<int:server_id>', methods=['GET'])
    @handle_api_error
    def api_compare_server_config(server_id: int):
        """API endpoint to compare server configuration"""
        result = api_service.compare_server_config(server_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to compare server configuration'}), 500

    @bp.route('/api/config/sync', methods=['POST'])
    @handle_api_error
    def api_sync_configurations():
        """API endpoint to sync configurations"""
        data = request.get_json()
        server_ids = data.get('server_ids') if data else None
        sync_direction = data.get('sync_direction', 'config_to_db') if data else 'config_to_db'
        remove_orphaned = data.get('remove_orphaned', False) if data else False

        result = api_service.sync_configurations(server_ids, sync_direction, remove_orphaned)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to sync configurations'}), 500

    # ========== Task Management API Endpoints ==========

    @bp.route('/api/tasks', methods=['GET'])
    @handle_api_error
    def api_get_tasks():
        """API endpoint to get tasks"""
        skip = request.args.get('skip', 0, type=int)
        limit = request.args.get('limit', 100, type=int)
        result = api_service.get_tasks(skip, limit)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to get tasks'}), 500

    @bp.route('/api/tasks/<int:task_id>', methods=['GET'])
    @handle_api_error
    def api_get_task_by_id(task_id: int):
        """API endpoint to get task by ID"""
        result = api_service.get_task(task_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Task not found'}), 404

    @bp.route('/api/tasks/expiry-check', methods=['POST'])
    @handle_api_error
    def api_trigger_expiry_check():
        """API endpoint to trigger expiry check"""
        server_id = request.args.get('server_id', type=int)
        result = api_service.trigger_expiry_check(server_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to trigger expiry check'}), 500

    @bp.route('/api/tasks/expiry/summary', methods=['GET'])
    @handle_api_error
    def api_get_expiry_summary():
        """API endpoint to get expiry summary"""
        result = api_service.get_expiry_summary()
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to get expiry summary'}), 500

    # ========== Health Monitoring API Endpoints ==========

    @bp.route('/api/health', methods=['GET'])
    @handle_api_error
    def api_get_health_dashboard():
        """API endpoint to get health dashboard"""
        result = api_service.get_health_dashboard()
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to get health dashboard'}), 500

    @bp.route('/api/health/detailed', methods=['GET'])
    @handle_api_error
    def api_get_detailed_health():
        """API endpoint to get detailed health check"""
        result = api_service.get_detailed_health()
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to get detailed health'}), 500

    @bp.route('/api/health/expiry-summary', methods=['GET'])
    @handle_api_error
    def api_get_expiry_health():
        """API endpoint to get expiry health check"""
        result = api_service.get_expiry_health()
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to get expiry health'}), 500

    @bp.route('/api/health/servers/<int:server_id>/refresh', methods=['POST'])
    @handle_api_error
    def api_refresh_server_health(server_id: int):
        """API endpoint to refresh server health"""
        result = api_service.refresh_server_health(server_id)
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to refresh server health'}), 500

    @bp.route('/api/health/refresh-all-servers', methods=['POST'])
    @handle_api_error
    def api_refresh_all_servers_health():
        """API endpoint to refresh all servers health"""
        result = api_service.refresh_all_servers_health()
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to refresh all servers health'}), 500

    @bp.route('/api/health/ssh-pool', methods=['GET'])
    @handle_api_error
    def api_get_ssh_pool_status():
        """API endpoint to get SSH pool status"""
        result = api_service.get_ssh_pool_status()
        if result:
            return jsonify(result)
        return jsonify({'error': 'Failed to get SSH pool status'}), 500

    @bp.route('/api/test-ssh-credentials', methods=['POST'])
    @login_required
    @handle_api_error
    def test_ssh_credentials():
        """Test SSH credentials without creating a server"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['host', 'username']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'success': False, 'message': f'Missing required field: {field}'}), 400

            # Check authentication method
            if not data.get('password') and not data.get('private_key'):
                return jsonify({'success': False, 'message': 'Either password or private_key is required'}), 400

            # Try the API service first
            result = api_service.test_server_credentials(data)

            # If API service fails, try local SSH test
            if not result or not result.get('success'):
                logger.info("API service test failed, trying local SSH test")
                result = test_ssh_connection_local(data)

            if result and result.get('success'):
                return jsonify({
                    'success': True,
                    'message': 'SSH connection successful',
                    'details': {
                        'host': data['host'],
                        'port': data.get('port', 22),
                        'username': data['username'],
                        'auth_method': 'SSH Key' if data.get('private_key') else 'Password',
                        'connection_time': result.get('connection_time', 'N/A'),
                        'test_method': result.get('test_method', 'API')
                    }
                })
            else:
                error_msg = result.get('message') if result else 'Connection failed'
                return jsonify({
                    'success': False,
                    'message': error_msg,
                    'details': result.get('details') if result else None
                })

        except Exception as e:
            logger.error(f"SSH credentials test error: {e}")
            return jsonify({'success': False, 'message': f'Test failed: {str(e)}'}), 500

    @bp.route('/api/test-xray-service', methods=['POST'])
    @login_required
    @handle_api_error
    def test_xray_service():
        """Test Xray service configuration and status"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['host', 'username']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'success': False, 'message': f'Missing required field: {field}'}), 400

            # Check authentication method
            if not data.get('password') and not data.get('private_key'):
                return jsonify({'success': False, 'message': 'Either password or private_key is required'}), 400

            # Set defaults for Xray configuration
            xray_config_path = data.get('xray_config_path', '/etc/xray/config.json')
            xray_service_name = data.get('xray_service_name', 'xray')

            # Try the API service first
            result = api_service.test_xray_service_config({
                'host': data['host'],
                'port': data.get('port', 22),
                'username': data['username'],
                'password': data.get('password'),
                'private_key': data.get('private_key'),
                'private_key_passphrase': data.get('private_key_passphrase'),
                'xray_config_path': xray_config_path,
                'xray_service_name': xray_service_name
            })

            # If API service fails, try local test
            if not result or not result.get('success'):
                logger.info("API service Xray test failed, trying local test")
                result = test_xray_service_local(data)

            if result:
                success = result.get('success', False)
                details = {
                    'config_path': xray_config_path,
                    'service_name': xray_service_name,
                    'config_exists': result.get('config_exists', False),
                    'config_valid': result.get('config_valid', False),
                    'service_exists': result.get('service_exists', False),
                    'service_active': result.get('service_active', False),
                    'service_status': result.get('service_status', 'unknown')
                }

                if success:
                    return jsonify({
                        'success': True,
                        'message': 'Xray service configuration is valid and service is running',
                        'details': details
                    })
                else:
                    issues = []
                    if not result.get('config_exists'):
                        issues.append(f'Config file not found at {xray_config_path}')
                    if not result.get('config_valid'):
                        issues.append('Config file is not valid JSON')
                    if not result.get('service_exists'):
                        issues.append(f'Service {xray_service_name} not found')
                    if not result.get('service_active'):
                        issues.append(f'Service {xray_service_name} is not active')

                    return jsonify({
                        'success': False,
                        'message': '; '.join(issues) if issues else 'Xray service test failed',
                        'details': details
                    })
            else:
                return jsonify({
                    'success': False,
                    'message': 'Failed to test Xray service configuration'
                })

        except Exception as e:
            logger.error(f"Xray service test error: {e}")
            return jsonify({'success': False, 'message': f'Test failed: {str(e)}'}), 500

    # ========== SSH User Management Routes ==========

    @bp.route('/ssh-users')
    @login_required
    def ssh_users():
        """SSH User Management Dashboard"""
        try:
            # Fetch servers and normalize response shape (longer timeout to reduce read timeouts)
            servers_raw = api_service.get_servers(timeout=10)
            servers = []

            if isinstance(servers_raw, list):
                servers = servers_raw
            elif isinstance(servers_raw, dict):
                for key in ('servers', 'items', 'data', 'results'):
                    value = servers_raw.get(key)
                    if isinstance(value, list):
                        servers = value
                        break

            # Fallback to local servers file if API returned nothing
            if not servers:
                servers_file = plugin.config.get('server_config', {}).get('servers_file', 'configs/services/vpn_servers.json')
                try:
                    if os.path.exists(servers_file):
                        with open(servers_file, 'r', encoding='utf-8') as f:
                            file_servers = json.load(f)
                            if isinstance(file_servers, list):
                                servers = file_servers
                except Exception as fe:
                    logger.warning(f"Failed to load servers from {servers_file}: {fe}")

            if not servers:
                flash('No servers found. Please add servers first.', 'warning')
                return redirect(url_for('vpn.servers'))

            return render_template('vpn_ssh_users.html', servers=servers)
        except Exception as e:
            logger.error(f"Error loading SSH users page: {e}")
            flash(f'Error loading SSH users: {str(e)}', 'error')
            return redirect(url_for('vpn.health_dashboard'))

    @bp.route('/api/servers/<int:server_id>/ssh-users/members')
    def api_get_ssh_users(server_id):
        """API endpoint to get SSH users for a server"""
        try:
            result = api_service.get_ssh_users(server_id)
            if result:
                return jsonify(result)
            else:
                return jsonify({'error': 'Failed to get SSH users'}), 500
        except Exception as e:
            logger.error(f"Error getting SSH users for server {server_id}: {e}")
            return jsonify({'error': str(e)}), 500


    @bp.route('/api/servers/<int:server_id>/ssh-users', methods=['POST'])
    def api_create_ssh_user(server_id):
        """API endpoint to create a new SSH user on a server"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'error': 'No data provided'}), 400

            username = data.get('username')
            password = data.get('password')
            expires_in_days = data.get('expires_in_days')
            expiry_date = data.get('expiry_date')
            shell = data.get('shell', '/bin/false')
            create_home = data.get('create_home', False)

            if not username or not password:
                return jsonify({'error': 'username and password are required'}), 400

            # Call API service
            result = api_service.create_ssh_user(
                server_id=server_id,
                username=username,
                password=password,
                expires_in_days=expires_in_days,
                expiry_date=expiry_date,
                shell=shell,
                create_home=create_home
            )

            if result:
                return jsonify(result)
            else:
                return jsonify({'error': 'Failed to create SSH user'}), 500
        except ValueError as ve:
            return jsonify({'error': str(ve)}), 400
        except Exception as e:
            logger.error(f"Error creating SSH user for server {server_id}: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/api/servers/<int:server_id>/ssh-users/sessions')
    def api_get_ssh_sessions(server_id):
        """API endpoint to get SSH sessions for a server"""
        try:
            result = api_service.get_ssh_sessions(server_id)
            if result:
                return jsonify(result)
            else:
                return jsonify({'error': 'Failed to get SSH sessions'}), 500
        except Exception as e:
            logger.error(f"Error getting SSH sessions for server {server_id}: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/api/servers/<int:server_id>/ssh-users/renew', methods=['POST'])
    def api_renew_ssh_user(server_id):
        """API endpoint to renew SSH user"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'error': 'No data provided'}), 400

            username = data.get('username')
            days = data.get('days')
            unlock = data.get('unlock', True)

            if not username or not days:
                return jsonify({'error': 'Username and days are required'}), 400

            result = api_service.renew_ssh_user(server_id, username, days, unlock)
            if result:
                return jsonify(result)
            else:
                return jsonify({'error': 'Failed to renew SSH user'}), 500
        except Exception as e:
            logger.error(f"Error renewing SSH user for server {server_id}: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/api/servers/<int:server_id>/ssh-users/limit-violations')
    def api_get_ssh_limit_violations(server_id):
        """API endpoint to get SSH limit violations for a server"""
        try:
            result = api_service.get_ssh_limit_violations(server_id)
            if result:
                return jsonify(result)
            else:
                return jsonify({'error': 'Failed to get SSH limit violations'}), 500
        except Exception as e:
            logger.error(f"Error getting SSH limit violations for server {server_id}: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/api/servers/<int:server_id>/ssh-users/auto-delete-expired', methods=['POST'])
    def api_auto_delete_expired_ssh_users(server_id):
        """API endpoint to auto delete expired SSH users"""
        try:
            result = api_service.auto_delete_expired_ssh_users(server_id)
            if result:
                return jsonify(result)
            else:
                return jsonify({'error': 'Failed to delete expired SSH users'}), 500
        except Exception as e:
            logger.error(f"Error deleting expired SSH users for server {server_id}: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/api/servers/<int:server_id>/ssh-users/autokill/status')
    def api_get_ssh_autokill_status(server_id):
        """API endpoint to get SSH autokill status for a server"""
        try:
            result = api_service.get_ssh_autokill_status(server_id)
            if result:
                return jsonify(result)
            else:
                return jsonify({'error': 'Failed to get SSH autokill status'}), 500
        except Exception as e:
            logger.error(f"Error getting SSH autokill status for server {server_id}: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/api/servers/<int:server_id>/ssh-users/autokill/configure', methods=['POST'])
    def api_configure_ssh_autokill(server_id):
        """API endpoint to configure SSH autokill settings"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'error': 'No data provided'}), 400

            interval_minutes = data.get('interval_minutes')
            max_sessions = data.get('max_sessions')

            if not interval_minutes or not max_sessions:
                return jsonify({'error': 'interval_minutes and max_sessions are required'}), 400

            if interval_minutes not in [5, 10, 15]:
                return jsonify({'error': 'interval_minutes must be 5, 10, or 15'}), 400

            result = api_service.configure_ssh_autokill(server_id, interval_minutes, max_sessions)
            if result:
                return jsonify(result)
            else:
                return jsonify({'error': 'Failed to configure SSH autokill'}), 500
        except Exception as e:
            logger.error(f"Error configuring SSH autokill for server {server_id}: {e}")
            return jsonify({'error': str(e)}), 500

    return bp
