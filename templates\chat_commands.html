{% extends "base.html" %}

{% block title %}Chat Commands - Admin Panel{% endblock %}

{% block header %}
<i class="fas fa-comments mr-2"></i>Chat Commands
{% endblock %}

{% block content %}
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <p class="text-gray-600 mb-4">Manage chat commands for automated responses in Shopee conversations</p>
</div>

<!-- Status Card -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-info-circle mr-2 text-blue-500"></i>Status
        </h3>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="space-y-3">
            <div>
                <span class="font-medium text-gray-700">Webhook Status:</span>
                {% if webhook_config.enabled %}
                    <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">Enabled</span>
                {% else %}
                    <span class="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">Disabled</span>
                {% endif %}
            </div>
            <div>
                <span class="font-medium text-gray-700">Debug Mode:</span>
                {% if debug_config.enabled %}
                    <span class="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">Enabled</span>
                {% else %}
                    <span class="ml-2 px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">Disabled</span>
                {% endif %}
            </div>
        </div>
        <div class="space-y-3">
            <div>
                <span class="font-medium text-gray-700">Command Prefix:</span>
                <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">{{ command_config.prefix }}</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">Total Commands:</span>
                <span class="ml-2 px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">{{ commands|length }}</span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200" onclick="testWebhook()">
                <i class="fas fa-plug mr-2"></i>Test Webhook
            </button>
            <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200" onclick="showAddCommandModal()">
                <i class="fas fa-plus mr-2"></i>Add Command
            </button>
        </div>
    </div>
</div>

<!-- Configuration Forms -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <!-- Webhook Configuration -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-webhook mr-2 text-blue-500"></i>Webhook Config
            </h3>
        </div>
        <form id="webhookConfigForm" class="space-y-4">
            <div class="flex items-center">
                <input type="checkbox" id="webhookEnabled" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if webhook_config.enabled %}checked{% endif %}>
                <label for="webhookEnabled" class="ml-2 block text-sm text-gray-700">
                    Enable Webhook
                </label>
            </div>

            <div>
                <label for="shopeeApiUrl" class="block text-sm font-medium text-gray-700 mb-1">Shopee API Base URL</label>
                <input type="url" id="shopeeApiUrl"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       value="{{ webhook_config.shopee_api_base_url }}"
                       placeholder="https://shop.api.limjianhui.com">
            </div>

            <div>
                <label for="steamcodeToolUrl" class="block text-sm font-medium text-gray-700 mb-1">SteamCodeTool Base URL</label>
                <input type="url" id="steamcodeToolUrl"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       value="{{ webhook_config.steamcodetool_base_url }}"
                       placeholder="https://your-steamcodetool.com">
            </div>

            <div class="flex items-center">
                <input type="checkbox" id="autoRegister" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if webhook_config.auto_register %}checked{% endif %}>
                <label for="autoRegister" class="ml-2 block text-sm text-gray-700">
                    Auto Register Webhook
                </label>
            </div>

            <div class="grid grid-cols-3 gap-4">
                <div>
                    <label for="retryCount" class="block text-sm font-medium text-gray-700 mb-1">Retry Count</label>
                    <input type="number" id="retryCount" min="0" max="10"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           value="{{ webhook_config.retry_count }}"
                           placeholder="3">
                </div>
                <div>
                    <label for="retryDelay" class="block text-sm font-medium text-gray-700 mb-1">Retry Delay (seconds)</label>
                    <input type="number" id="retryDelay" min="1" max="60"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           value="{{ webhook_config.retry_delay }}"
                           placeholder="5">
                </div>
                <div>
                    <label for="timeout" class="block text-sm font-medium text-gray-700 mb-1">Timeout (seconds)</label>
                    <input type="number" id="timeout" min="5" max="120"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           value="{{ webhook_config.timeout }}"
                           placeholder="30">
                </div>
            </div>

            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-save mr-2"></i>Save Webhook
            </button>
        </form>
    </div>

    <!-- Debug Configuration -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-bug mr-2 text-yellow-500"></i>Debug Config
            </h3>
        </div>
        <form id="debugConfigForm" class="space-y-4">
            <div class="flex items-center">
                <input type="checkbox" id="debugEnabled" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if debug_config.enabled %}checked{% endif %}>
                <label for="debugEnabled" class="ml-2 block text-sm text-gray-700">
                    Enable Debug Mode
                </label>
            </div>

            <div class="flex items-center">
                <input type="checkbox" id="logWebhookData" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if debug_config.log_webhook_data %}checked{% endif %}>
                <label for="logWebhookData" class="ml-2 block text-sm text-gray-700">
                    Log Webhook Data
                </label>
            </div>

            <div class="flex items-center">
                <input type="checkbox" id="logMessageParsing" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if debug_config.log_message_parsing %}checked{% endif %}>
                <label for="logMessageParsing" class="ml-2 block text-sm text-gray-700">
                    Log Message Parsing
                </label>
            </div>

            <div class="flex items-center">
                <input type="checkbox" id="logCommandProcessing" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if debug_config.log_command_processing %}checked{% endif %}>
                <label for="logCommandProcessing" class="ml-2 block text-sm text-gray-700">
                    Log Command Processing
                </label>
            </div>

            <div class="flex items-center">
                <input type="checkbox" id="logResponseGeneration" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if debug_config.log_response_generation %}checked{% endif %}>
                <label for="logResponseGeneration" class="ml-2 block text-sm text-gray-700">
                    Log Response Generation
                </label>
            </div>

            <div class="border-t pt-4 mt-4">
                <h4 class="text-sm font-semibold text-gray-700 mb-3">System Logs Control</h4>

                <div class="flex items-center mb-2">
                    <input type="checkbox" id="logInfoMessages" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                           {% if debug_config.log_info_messages %}checked{% endif %}>
                    <label for="logInfoMessages" class="ml-2 block text-sm text-gray-700">
                        Log Info Messages
                    </label>
                </div>

                <div class="flex items-center mb-2">
                    <input type="checkbox" id="logAccessAudit" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                           {% if debug_config.log_access_audit %}checked{% endif %}>
                    <label for="logAccessAudit" class="ml-2 block text-sm text-gray-700">
                        Log Access Audit (HTTP Requests)
                    </label>
                </div>

                <div class="flex items-center mb-2">
                    <input type="checkbox" id="logWerkzeug" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                           {% if debug_config.log_werkzeug %}checked{% endif %}>
                    <label for="logWerkzeug" class="ml-2 block text-sm text-gray-700">
                        Log Werkzeug (Flask Server)
                    </label>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="logServiceOperations" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                           {% if debug_config.log_service_operations %}checked{% endif %}>
                    <label for="logServiceOperations" class="ml-2 block text-sm text-gray-700">
                        Log Service Operations
                    </label>
                </div>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4">
                <div class="flex">
                    <i class="fas fa-exclamation-triangle text-yellow-500 mt-0.5 mr-2"></i>
                    <div class="text-sm text-yellow-700">
                        <strong>Note:</strong> Disabling these logs will reduce console spam but may make debugging harder.
                        Enable only what you need for troubleshooting.
                    </div>
                </div>
            </div>

            <button type="submit" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-save mr-2"></i>Save Debug Settings
            </button>
        </form>
    </div>

    <!-- Command Configuration -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-terminal mr-2 text-purple-500"></i>Command Config
            </h3>
        </div>
        <form id="commandConfigForm" class="space-y-4">
            <div>
                <label for="commandPrefix" class="block text-sm font-medium text-gray-700 mb-1">Command Prefix</label>
                <input type="text" id="commandPrefix"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       value="{{ command_config.command_prefix }}"
                       placeholder="#" maxlength="5">
            </div>

            <div class="flex items-center">
                <input type="checkbox" id="caseSensitive" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if command_config.case_sensitive %}checked{% endif %}>
                <label for="caseSensitive" class="ml-2 block text-sm text-gray-700">
                    Case Sensitive
                </label>
            </div>

            <div class="flex items-center">
                <input type="checkbox" id="sendImagesFirst" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if command_config.send_images_first %}checked{% endif %}>
                <label for="sendImagesFirst" class="ml-2 block text-sm text-gray-700">
                    Send Images First (before text)
                </label>
            </div>

            <div class="grid grid-cols-3 gap-4">
                <div>
                    <label for="maxResponseLength" class="block text-sm font-medium text-gray-700 mb-1">Max Response Length</label>
                    <input type="number" id="maxResponseLength" min="100" max="5000"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           value="{{ command_config.max_response_length }}"
                           placeholder="2000">
                </div>
                <div>
                    <label for="maxImagesPerResponse" class="block text-sm font-medium text-gray-700 mb-1">Max Images Per Response</label>
                    <input type="number" id="maxImagesPerResponse" min="1" max="10"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           value="{{ command_config.max_images_per_response }}"
                           placeholder="5">
                </div>
                <div>
                    <label for="messageSplitLimit" class="block text-sm font-medium text-gray-700 mb-1">Message Split Limit</label>
                    <input type="number" id="messageSplitLimit" min="100" max="1000"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           value="{{ command_config.message_split_limit }}"
                           placeholder="600">
                </div>
            </div>

            <button type="submit" class="w-full bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-save mr-2"></i>Save Command
            </button>
        </form>
    </div>
</div>

<!-- Commands List -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-list mr-2 text-green-500"></i>Commands
        </h3>
        <div class="flex space-x-2">
            <input type="text" id="searchCommands" placeholder="Search commands..." 
                   class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200" onclick="showAddCommandModal()">
                <i class="fas fa-plus mr-2"></i>Add
            </button>
        </div>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200" id="commandsTable">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Command</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Response Type</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody id="commandsTableBody" class="bg-white divide-y divide-gray-200">
                <!-- Commands will be loaded here -->
            </tbody>
        </table>
    </div>
</div>

<!-- Add Command Modal -->
<div id="addCommandModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800" id="commandModalTitle">Add New Command</h3>
                <button onclick="closeAddCommandModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="commandForm" class="space-y-4">
                <input type="hidden" id="originalCommandName" value="">

                <div>
                    <label for="commandName" class="block text-sm font-medium text-gray-700 mb-1">Command Name</label>
                    <input type="text" id="commandName" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           placeholder="e.g., android_help">
                </div>

                <div>
                    <label for="commandDescription" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <input type="text" id="commandDescription" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Brief description of the command">
                </div>

                <div>
                    <label for="responseText" class="block text-sm font-medium text-gray-700 mb-1">Response Text</label>
                    <textarea id="responseText" rows="4" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              placeholder="The text response to send when this command is triggered"></textarea>
                </div>

                <div>
                    <label for="imageUrls" class="block text-sm font-medium text-gray-700 mb-1">Image URLs (one per line)</label>
                    <textarea id="imageUrls" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              placeholder="https://example.com/image1.jpg&#10;https://example.com/image2.jpg"></textarea>
                </div>

                <div>
                    <label for="requiredParams" class="block text-sm font-medium text-gray-700 mb-1">Required Parameters (comma-separated)</label>
                    <input type="text" id="requiredParams"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           placeholder="param1, param2">
                </div>

                <div class="space-y-3">
                    <div class="flex items-center">
                        <input type="checkbox" id="commandEnabled" checked
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="commandEnabled" class="ml-2 block text-sm text-gray-700">
                            Enable this command
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="sendImagesFirstCommand"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="sendImagesFirstCommand" class="ml-2 block text-sm text-gray-700">
                            Send images before text for this command
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="markAsUnread"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="markAsUnread" class="ml-2 block text-sm text-gray-700">
                            Mark conversation as unread after sending response
                        </label>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeAddCommandModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition duration-200">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition duration-200">
                        Save Command
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let commands = {};

// Load commands on page load
document.addEventListener('DOMContentLoaded', function() {
    loadCommands();

    // Form submission handlers
    document.getElementById('webhookConfigForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveWebhookConfig();
    });

    document.getElementById('debugConfigForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveDebugConfig();
    });

    document.getElementById('commandConfigForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveCommandConfig();
    });

    document.getElementById('commandForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveCommand();
    });

    // Search functionality
    document.getElementById('searchCommands').addEventListener('input', function(e) {
        filterCommands(e.target.value);
    });
});

// Load commands function
async function loadCommands() {
    try {
        const response = await fetch('/chat-commands/api/commands');
        const result = await response.json();

        if (result.success) {
            commands = result.commands;
            displayCommands(commands);
        } else {
            showAlert('Error loading commands: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('Error loading commands: ' + error.message, 'error');
    }
}

// Display commands in table
function displayCommands(commandsToShow) {
    const tbody = document.getElementById('commandsTableBody');
    tbody.innerHTML = '';

    if (Object.keys(commandsToShow).length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">No commands found</td></tr>';
        return;
    }

    Object.entries(commandsToShow).forEach(([commandName, command]) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">#${command.command}</div>
            </td>
            <td class="px-6 py-4">
                <div class="text-sm text-gray-900">${command.description || 'No description'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${command.response_text ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}">
                    ${command.response_text ? 'Text' : 'None'}
                </span>
                ${command.image_urls && command.image_urls.length > 0 ?
                    `<span class="ml-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                        ${command.image_urls.length} image${command.image_urls.length > 1 ? 's' : ''}
                    </span>` : ''}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${command.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${command.enabled ? 'Enabled' : 'Disabled'}
                </span>
                ${command.locked ? `<span class=\"ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-200 text-gray-700\" title=\"Locked by plugin\">Locked</span>` : ''}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                ${command.locked ? '' : `<button class=\"text-blue-600 hover:text-blue-900\" onclick=\"editCommand('${command.command}')\" title=\"Edit\">\n                    <i class=\"fas fa-edit\"></i>\n                </button>`}
                <button class="text-green-600 hover:text-green-900" onclick="testCommand('${command.command}')" title="Test">
                    <i class="fas fa-play"></i>
                </button>
                ${command.locked ? '' : `<button class=\"text-red-600 hover:text-red-900\" onclick=\"deleteCommand('${command.command}')\" title=\"Delete\">\n                    <i class=\"fas fa-trash\"></i>\n                </button>`}
                ${command.locked ? `<i class=\"fas fa-lock text-gray-400\" title=\"Locked command\"></i>` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Filter commands based on search
function filterCommands(searchTerm) {
    if (!searchTerm) {
        displayCommands(commands);
        return;
    }

    const filtered = {};
    Object.entries(commands).forEach(([name, command]) => {
        if (name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            command.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
            command.response_text.toLowerCase().includes(searchTerm.toLowerCase())) {
            filtered[name] = command;
        }
    });

    displayCommands(filtered);
}

// Show add command modal
function showAddCommandModal() {
    document.getElementById('commandModalTitle').textContent = 'Add New Command';
    document.getElementById('originalCommandName').value = '';
    document.getElementById('commandForm').reset();
    document.getElementById('commandEnabled').checked = true;
    document.getElementById('addCommandModal').classList.remove('hidden');
}

// Close add command modal
function closeAddCommandModal() {
    document.getElementById('addCommandModal').classList.add('hidden');
}

// Edit command
function editCommand(commandName) {
    const command = commands[commandName];
    if (!command) return;

    document.getElementById('commandModalTitle').textContent = 'Edit Command';
    document.getElementById('originalCommandName').value = commandName;
    document.getElementById('commandName').value = command.command;
    document.getElementById('commandDescription').value = command.description;
    document.getElementById('responseText').value = command.response_text;
    document.getElementById('imageUrls').value = (command.image_urls || []).join('\n');
    document.getElementById('requiredParams').value = (command.required_params || []).join(', ');
    document.getElementById('commandEnabled').checked = command.enabled;
    document.getElementById('sendImagesFirstCommand').checked = command.send_images_first || false;
    document.getElementById('markAsUnread').checked = command.mark_as_unread || false;

    document.getElementById('addCommandModal').classList.remove('hidden');
}

// Save command
async function saveCommand() {
    const originalName = document.getElementById('originalCommandName').value;
    const isEdit = originalName !== '';

    const commandData = {
        command: document.getElementById('commandName').value,
        description: document.getElementById('commandDescription').value,
        response_text: document.getElementById('responseText').value,
        image_urls: document.getElementById('imageUrls').value.split('\n').filter(url => url.trim() !== ''),
        required_params: document.getElementById('requiredParams').value.split(',').map(p => p.trim()).filter(p => p !== ''),
        enabled: document.getElementById('commandEnabled').checked,
        send_images_first: document.getElementById('sendImagesFirstCommand').checked,
        mark_as_unread: document.getElementById('markAsUnread').checked
    };

    try {
        const url = isEdit ? `/chat-commands/api/commands/${originalName}` : '/chat-commands/api/commands';
        const method = isEdit ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(commandData)
        });

        const result = await response.json();

        if (result.success) {
            showAlert(result.message, 'success');
            closeAddCommandModal();
            loadCommands(); // Reload the commands list
        } else {
            showAlert('Error: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('Error saving command: ' + error.message, 'error');
    }
}

// Test command
async function testCommand(commandName) {
    try {
        const response = await fetch('/chat-commands/api/test-command', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ command: commandName })
        });
        const result = await response.json();

        if (result.success) {
            showAlert(`Command test successful! Generated ${result.responses.length} response(s)`, 'success');
        } else {
            showAlert('Command test failed: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('Error testing command: ' + error.message, 'error');
    }
}

// Delete command
async function deleteCommand(commandName) {
    if (!confirm('Are you sure you want to delete this command?')) {
        return;
    }

    try {
        const response = await fetch(`/chat-commands/api/commands/${commandName}`, {
            method: 'DELETE'
        });
        const result = await response.json();

        if (result.success) {
            showAlert('Command deleted successfully!', 'success');
            loadCommands(); // Reload the commands list
        } else {
            showAlert('Error deleting command: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('Error deleting command: ' + error.message, 'error');
    }
}

// Save webhook configuration
async function saveWebhookConfig() {
    const configData = {
        enabled: document.getElementById('webhookEnabled').checked,
        shopee_api_base_url: document.getElementById('shopeeApiUrl').value,
        steamcodetool_base_url: document.getElementById('steamcodeToolUrl').value,
        auto_register: document.getElementById('autoRegister').checked,
        retry_count: parseInt(document.getElementById('retryCount').value) || 3,
        retry_delay: parseInt(document.getElementById('retryDelay').value) || 5,
        timeout: parseInt(document.getElementById('timeout').value) || 30
    };

    try {
        const response = await fetch('/chat-commands/api/webhook-config', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configData)
        });

        const result = await response.json();

        if (result.success) {
            showAlert(result.message, 'success');
        } else {
            showAlert('Error: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('Failed to save webhook configuration: ' + error.message, 'error');
    }
}

// Save debug configuration
async function saveDebugConfig() {
    const configData = {
        enabled: document.getElementById('debugEnabled').checked,
        log_webhook_data: document.getElementById('logWebhookData').checked,
        log_message_parsing: document.getElementById('logMessageParsing').checked,
        log_command_processing: document.getElementById('logCommandProcessing').checked,
        log_response_generation: document.getElementById('logResponseGeneration').checked,
        log_info_messages: document.getElementById('logInfoMessages').checked,
        log_access_audit: document.getElementById('logAccessAudit').checked,
        log_werkzeug: document.getElementById('logWerkzeug').checked,
        log_service_operations: document.getElementById('logServiceOperations').checked
    };

    try {
        const response = await fetch('/chat-commands/api/debug-config', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configData)
        });

        const result = await response.json();

        if (result.success) {
            showAlert(result.message, 'success');
        } else {
            showAlert('Error: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('Failed to save debug configuration: ' + error.message, 'error');
    }
}

// Save command configuration
async function saveCommandConfig() {
    const configData = {
        command_prefix: document.getElementById('commandPrefix').value,
        case_sensitive: document.getElementById('caseSensitive').checked,
        max_response_length: parseInt(document.getElementById('maxResponseLength').value) || 2000,
        max_images_per_response: parseInt(document.getElementById('maxImagesPerResponse').value) || 5,
        send_images_first: document.getElementById('sendImagesFirst').checked,
        message_split_limit: parseInt(document.getElementById('messageSplitLimit').value) || 600
    };

    try {
        const response = await fetch('/chat-commands/api/command-config', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configData)
        });

        const result = await response.json();

        if (result.success) {
            showAlert(result.message, 'success');
        } else {
            showAlert('Error: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('Failed to save command configuration: ' + error.message, 'error');
    }
}

// Test webhook function
async function testWebhook() {
    try {
        const response = await fetch('/chat-commands/api/webhook/test', {
            method: 'POST'
        });
        const result = await response.json();

        if (result.success) {
            showAlert('Webhook test successful!', 'success');
        } else {
            showAlert('Webhook test failed: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('Error testing webhook: ' + error.message, 'error');
    }
}

// Show alert function
function showAlert(message, type) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' :
        type === 'error' ? 'bg-red-100 border border-red-400 text-red-700' :
        'bg-blue-100 border border-blue-400 text-blue-700'
    }`;

    alertDiv.innerHTML = `
        <div class="flex items-center">
            <div class="flex-1">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 5000);
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('addCommandModal');
    if (event.target === modal) {
        closeAddCommandModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeAddCommandModal();
    }
});
</script>
{% endblock %}
