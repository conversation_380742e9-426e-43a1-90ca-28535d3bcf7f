<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Admin Panel{% endblock %}</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.25/css/jquery.dataTables.min.css">
    <script src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/alpinejs" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>

    <!-- Bootstrap 5 CSS and JS for plugin compatibility -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .nav-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .submenu-item {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .submenu-item:hover {
            background-color: rgba(255, 255, 255, 0.15);
        }

        /* Fix text wrapping in menu items */
        .nav-item, .submenu-item {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .nav-item span, .submenu-item span {
            display: flex;
            align-items: center;
        }

        /* Bootstrap and Tailwind compatibility fixes */
        .container-fluid {
            width: 100% !important;
            max-width: none !important;
        }

        /* Ensure Bootstrap modals work properly */
        .modal-backdrop {
            z-index: 1040 !important;
        }

        .modal {
            z-index: 1050 !important;
        }

        /* Fix button conflicts */
        .btn:not(.tailwind-btn) {
            display: inline-block !important;
        }
    </style>
    {% block extra_head %}{% endblock %}
</head>

<body class="bg-gray-100 min-h-screen flex">
    <!-- Sidebar -->
    <nav
        class="gradient-bg text-white w-64 space-y-6 py-7 px-2 absolute inset-y-0 left-0 transform -translate-x-full md:relative md:translate-x-0 transition duration-200 ease-in-out">
        <ul class="space-y-2">
            <!-- Dashboard -->
            <li>
                <a href="{{ url_for('admin.dashboard') }}"
                    class="block py-2.5 px-4 rounded transition duration-200 nav-item">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
            </li>

            <!-- Inventory -->
            <li x-data="{ open: false }">
                <button @click="open = !open"
                    class="w-full flex items-center justify-between py-2.5 px-4 rounded transition duration-200 nav-item">
                    <span><i class="fas fa-warehouse mr-2"></i>Inventory</span>
                    <i class="fas fa-chevron-down" x-bind:class="{ 'transform rotate-180': open }"></i>
                </button>
                <ul x-show="open" class="pl-4 space-y-2 mt-2">
                    <li>
                        <a href="{{ url_for('admin.stock') }}"
                            class="block py-2 px-4 rounded transition duration-200 submenu-item">
                            <i class="fas fa-boxes mr-2"></i>Stock Management
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('admin.inventory') }}"
                            class="block py-2 px-4 rounded transition duration-200 submenu-item">
                            <i class="fas fa-shipping-fast mr-2"></i>Shipment Record
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Plugins -->
            <li x-data="{ open: false }">
                <button @click="open = !open"
                    class="w-full flex items-center justify-between py-2.5 px-4 rounded transition duration-200 nav-item">
                    <span><i class="fas fa-puzzle-piece mr-2"></i>Plugins</span>
                    <i class="fas fa-chevron-down" x-bind:class="{ 'transform rotate-180': open }"></i>
                </button>
                <ul x-show="open" class="pl-4 space-y-2 mt-2">
                    <!-- Steam Plugin -->
                    <li x-data="{ steamOpen: false }">
                        <button @click="steamOpen = !steamOpen"
                            class="w-full flex items-center justify-between py-2 px-4 rounded transition duration-200 submenu-item">
                            <span><i class="fab fa-steam mr-2"></i>Steam</span>
                            <i class="fas fa-chevron-down text-xs" x-bind:class="{ 'transform rotate-180': steamOpen }"></i>
                        </button>
                        <ul x-show="steamOpen" class="pl-4 space-y-1 mt-1">
                            <li>
                                <a href="{{ url_for('admin.steam_settings') }}"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-cog mr-2"></i>Settings
                                </a>
                            </li>
                            <li>
                                <a href="{{ url_for('admin.credentials') }}"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-key mr-2"></i>Credentials
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Netflix Plugin -->
                    <li x-data="{ netflixOpen: false }">
                        <button @click="netflixOpen = !netflixOpen"
                            class="w-full flex items-center justify-between py-2 px-4 rounded transition duration-200 submenu-item">
                            <span><i class="fas fa-film mr-2"></i>Netflix</span>
                            <i class="fas fa-chevron-down text-xs" x-bind:class="{ 'transform rotate-180': netflixOpen }"></i>
                        </button>
                        <ul x-show="netflixOpen" class="pl-4 space-y-1 mt-1">
                            <li>
                                <a href="{{ url_for('admin.netflix_accounts') }}"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-user-circle mr-2"></i>Accounts
                                </a>
                            </li>
                            <li>
                                <a href="{{ url_for('admin.netflix_settings') }}"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-cog mr-2"></i>Settings
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Canva Plugin -->
                    <li>
                        <a href="{{ url_for('admin.canva_manage') }}"
                            class="block py-2 px-4 rounded transition duration-200 submenu-item">
                            <i class="fas fa-palette mr-2"></i>Canva Manage
                        </a>
                    </li>

                    <!-- AI Chat Plugin -->
                    {% if is_plugin_loaded('ai_chat') %}
                    <li>
                        <a href="{{ url_for('ai_chat.ai_chat_settings') }}"
                            class="block py-2 px-4 rounded transition duration-200 submenu-item">
                            <i class="fas fa-brain mr-2"></i>AI Chat
                        </a>
                    </li>
                    {% endif %}

                    <!-- Chat Commands Plugin -->
                    <li>
                        <a href="/chat-commands/"
                            class="block py-2 px-4 rounded transition duration-200 submenu-item">
                            <i class="fas fa-comments mr-2"></i>Chat Commands
                        </a>
                    </li>

                    <!-- Shopee Auto Boost Plugin -->
                    <li x-data="{ shopeeBoostOpen: false }">
                        <button @click="shopeeBoostOpen = !shopeeBoostOpen"
                            class="w-full flex items-center justify-between py-2 px-4 rounded transition duration-200 submenu-item">
                            <span><i class="fas fa-rocket mr-2"></i>Shopee Auto Boost</span>
                            <i class="fas fa-chevron-down text-xs" x-bind:class="{ 'transform rotate-180': shopeeBoostOpen }"></i>
                        </button>
                        <ul x-show="shopeeBoostOpen" class="pl-4 space-y-1 mt-1">
                            <li>
                                <a href="{{ url_for('admin.shopee_auto_boost_dashboard') }}"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                                </a>
                            </li>
                            <li>
                                <a href="{{ url_for('admin.shopee_auto_boost_products') }}"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-boxes mr-2"></i>Products
                                </a>
                            </li>
                            <li>
                                <a href="{{ url_for('admin.shopee_auto_boost_history') }}"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-history mr-2"></i>Boost History
                                </a>
                            </li>
                            <li>
                                <a href="{{ url_for('admin.shopee_auto_boost_settings') }}"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-cog mr-2"></i>Settings
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- VPN Config Generator Plugin -->
                    <li x-data="{ vpnConfigOpen: false }">
                        <button @click="vpnConfigOpen = !vpnConfigOpen"
                            class="w-full flex items-center justify-between py-2 px-4 rounded transition duration-200 submenu-item">
                            <span><i class="fas fa-cogs mr-2"></i>VPN Config Generator</span>
                            <i class="fas fa-chevron-down text-xs" x-bind:class="{ 'transform rotate-180': vpnConfigOpen }"></i>
                        </button>
                        <ul x-show="vpnConfigOpen" class="pl-4 space-y-1 mt-1">
                            <li>
                                <a href="/vpn-config-generator/"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                                </a>
                            </li>
                            <li>
                                <a href="/vpn-config-generator/telco-management"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-network-wired mr-2"></i>Telco Configuration
                                </a>
                            </li>
                            <li>
                                <a href="/vpn-config-generator/sku-restrictions"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-shield-alt mr-2"></i>SKU Restrictions
                                </a>
                            </li>
                            <li>
                                <a href="/vpn-config-generator/sku-tags"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-tags mr-2"></i>SKU Tags Management
                                </a>
                            </li>
                            <li>
                                <a href="/vpn-config-generator/admin/redemption-links"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-link mr-2"></i>Redemption Links
                                </a>
                            </li>
                            <li>
                                <a href="/vpn-config-generator/admin/redemption-links/bulk"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-layer-group mr-2"></i>Bulk Creation
                                </a>
                            </li>
                            <li>
                                <a href="/vpn-config-generator/admin/redemption-links/analytics"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-chart-bar mr-2"></i>Analytics
                                </a>
                            </li>
                        </ul>
                    </li>
 
                     <!-- VPN Plugin -->
                     <li x-data="{ vpnOpen: false }">
                         <button @click="vpnOpen = !vpnOpen"
                            class="w-full flex items-center justify-between py-2 px-4 rounded transition duration-200 submenu-item">
                            <span><i class="fas fa-shield-alt mr-2"></i>VPN</span>
                            <i class="fas fa-chevron-down text-xs" x-bind:class="{ 'transform rotate-180': vpnOpen }"></i>
                        </button>
                        <ul x-show="vpnOpen" class="pl-4 space-y-1 mt-1">
                            <li>
                                <a href="/admin/vpn/servers"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-server mr-2"></i>Servers
                                </a>
                            </li>
                            <li>
                                <a href="/admin/vpn/clients"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-users mr-2"></i>Clients
                                </a>
                            </li>
                            <li>
                                <a href="/admin/vpn/ssh-users"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-user-shield mr-2"></i>SSH User Management
                                </a>
                            </li>
                            <li>
                                <a href="/admin/vpn/settings"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-cog mr-2"></i>Settings
                                </a>
                            </li>

                        </ul>
                    </li>

                    <!-- OpenAI Plus Redeem Plugin -->
                    <li x-data="{ openaiOpen: false }">
                        <button @click="openaiOpen = !openaiOpen"
                            class="w-full flex items-center justify-between py-2 px-4 rounded transition duration-200 submenu-item">
                            <span><i class="fas fa-brain mr-2"></i>OpenAI Plus Redeem</span>
                            <i class="fas fa-chevron-down text-xs" x-bind:class="{ 'transform rotate-180': openaiOpen }"></i>
                        </button>
                        <ul x-show="openaiOpen" class="pl-4 space-y-1 mt-1">
                            <li>
                                <a href="/admin/openai-plus-redeem/"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                                </a>
                            </li>
                            <li>
                                <a href="/admin/openai-plus-redeem/accounts"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-user-circle mr-2"></i>Accounts
                                </a>
                            </li>
                            <li>
                                <a href="/admin/openai-plus-redeem/redemptions"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-ticket-alt mr-2"></i>Redemptions
                                </a>
                            </li>
                            <li>
                                <a href="/admin/openai-plus-redeem/cooldowns"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-clock mr-2"></i>Cooldown Management
                                </a>
                            </li>
                            <li>
                                <a href="/admin/openai-plus-redeem/settings"
                                    class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                                    <i class="fas fa-cog mr-2"></i>Settings
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Plugin Manager -->
                    <li>
                        <a href="{{ url_for('admin.plugins') }}"
                            class="block py-2 px-4 rounded transition duration-200 submenu-item">
                            <i class="fas fa-plug mr-2"></i>Plugin Manager
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Automation -->
            <li x-data="{ open: false }">
                <button @click="open = !open"
                    class="w-full flex items-center justify-between py-2.5 px-4 rounded transition duration-200 nav-item">
                    <span><i class="fas fa-robot mr-2"></i>Automation</span>
                    <i class="fas fa-chevron-down" x-bind:class="{ 'transform rotate-180': open }"></i>
                </button>
                <ul x-show="open" class="pl-4 space-y-2 mt-2">
                    <li>
                        <a href="{{ url_for('admin.auto_chat') }}"
                            class="block py-2 px-4 rounded transition duration-200 submenu-item">
                            <i class="fas fa-comments mr-2"></i>Auto Chat
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('admin.auto_reply') }}"
                            class="block py-2 px-4 rounded transition duration-200 submenu-item">
                            <i class="fas fa-reply-all mr-2"></i>Auto Reply
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('admin.self_redeem_skus') }}"
                            class="block py-2 px-4 rounded transition duration-200 submenu-item">
                            <i class="fas fa-tags mr-2"></i>Self Redeem SKUs
                        </a>
                    </li>
                </ul>
            </li>



            <!-- Finance -->
            <li x-data="{ open: false, showInvoiceMenu: false }" x-init="
                fetch('/admin/get_config')
                    .then(response => response.json())
                    .then(data => {
                        showInvoiceMenu = !!(data.CURLEC_API_KEY && data.CURLEC_SECRET_KEY);
                    })">
                <template x-if="showInvoiceMenu">
                    <button @click="open = !open"
                        class="w-full flex items-center justify-between py-2.5 px-4 rounded transition duration-200 nav-item">
                        <span><i class="fas fa-chart-line mr-2"></i>Finance</span>
                        <i class="fas fa-chevron-down" x-bind:class="{ 'transform rotate-180': open }"></i>
                    </button>
                </template>
                <ul x-show="open && showInvoiceMenu" class="pl-4 space-y-2 mt-2">
                    <li>
                        <a href="{{ url_for('admin.manual_invoice') }}"
                            class="block py-2 px-4 rounded transition duration-200 submenu-item">
                            <i class="fas fa-file-invoice mr-2"></i>Manual Invoice
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Tools -->
            <li x-data="{ open: false }">
                <button @click="open = !open"
                    class="w-full flex items-center justify-between py-2.5 px-4 rounded transition duration-200 nav-item">
                    <span><i class="fas fa-tools mr-2"></i>Tools</span>
                    <i class="fas fa-chevron-down" x-bind:class="{ 'transform rotate-180': open }"></i>
                </button>
                <ul x-show="open" class="pl-4 space-y-2 mt-2">
                    <li>
                        <a href="/fake_order"
                            class="block py-2 px-4 rounded transition duration-200 submenu-item">
                            <i class="fas fa-vial mr-2"></i>Fake Order
                        </a>
                    </li>
                </ul>
            </li>

            <!-- System -->
            <li>
                <a href="{{ url_for('admin.system_config') }}"
                    class="block py-2.5 px-4 rounded transition duration-200 nav-item">
                    <i class="fas fa-cog mr-2"></i>System
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col overflow-hidden">
        <header class="bg-white shadow-lg">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
                <h1 class="text-3xl font-bold text-gray-900">
                    {% block header %}{% endblock %}
                </h1>
                <button class="md:hidden gradient-bg text-white px-3 py-2 rounded-md text-sm font-medium"
                    onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </header>
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200">
            <div class="container mx-auto px-6 py-8">
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('nav');
            sidebar.classList.toggle('-translate-x-full');
        }
    </script>
    {% block extra_scripts %}{% endblock %}
</body>

</html>