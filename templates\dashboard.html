{% extends "base.html" %}

{% block title %}Dashboard{% endblock %}

{% block header %}
Dashboard
{% endblock %}

{% block content %}
<!-- Widget containers for different positions -->
<div id="widget-container-header"></div>

<div id="widget-container-main">
    <!-- Widgets will be dynamically loaded here -->
</div>

<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <div class="md:col-span-2">
        <div id="widget-container-content">
            <!-- Main content widgets -->
        </div>
    </div>
    <div>
        <div id="widget-container-sidebar">
            <!-- Sidebar widgets -->
        </div>
    </div>
</div>

<div id="widget-container-footer"></div>

<!-- Widget Styles -->
<style>
    /* Widget container styles */
    .widget-wrapper {
        margin-bottom: 1.5rem;
    }
    
    .widget-wrapper.widget-size-small {
        grid-column: span 1;
    }
    
    .widget-wrapper.widget-size-medium {
        grid-column: span 2;
    }
    
    .widget-wrapper.widget-size-large {
        grid-column: span 3;
    }
    
    .widget-wrapper.widget-size-full {
        grid-column: 1 / -1;
    }
    
    .widget-container {
        min-height: 50px;
    }
    
    .widget-loading {
        text-align: center;
        padding: 2rem;
        color: #6b7280;
    }
    
    .widget-error {
        text-align: center;
        padding: 2rem;
        color: #ef4444;
    }
    
    #widget-container-main {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }
    
    /* DataTable styles */
    .dataTables_wrapper {
        font-family: 'Arial', sans-serif;
        padding-left: 30px;
        padding-right: 30px;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        margin: 15px 0;
        font-size: 14px;
    }

    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #e2e8f0;
        border-radius: 4px;
        padding: 5px 10px;
    }

    .dataTables_wrapper .dataTables_length select {
        border: 1px solid #e2e8f0;
        border-radius: 4px;
        padding: 5px 25px 5px 10px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23000' d='M0 0l4 4 4-4z'/%3E%3C/svg%3E") no-repeat right 10px center/8px 8px;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        border: none;
        padding: 5px 10px;
        margin: 0 2px;
        border-radius: 4px;
        background-color: #f7fafc;
        color: #4a5568;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current,
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background-color: #4a5568;
        color: white !important;
    }

    table.dataTable thead th {
        border-bottom: 2px solid #e2e8f0;
        font-weight: 600;
    }

    table.dataTable tbody td {
        padding: 12px 10px;
    }

    table.dataTable tbody tr:hover {
        background-color: #f7fafc;
    }
</style>

<script src="/static/js/widget-loader.js" defer></script>

<script>
$(document).ready(function () {
    // The widget loader will automatically initialize on page load
    
    // Set up global event handlers for widget interactions
    
    // Handle service details button clicks for health widget
    $(document).on('click', '.service-details-btn', function() {
        const serviceKey = $(this).data('service');
        showServiceDetails(serviceKey);
    });
    
    // Function to show service details modal
    async function showServiceDetails(serviceKey) {
        try {
            const resp = await fetch('/admin/api/health/service/' + serviceKey);
            if (!resp.ok) {
                throw new Error('HTTP ' + resp.status);
            }
            const response = await resp.json();
            if (response.success) {
                const service = response.data;
                let detailsHtml = `
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-lg font-semibold">${service.name} - Service Details</h4>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h5 class="font-medium mb-2">Service Information</h5>
                                    <div class="space-y-1 text-sm">
                                        <div><strong>Name:</strong> ${service.name}</div>
                                        <div><strong>Base URL:</strong> <code class="bg-gray-100 px-1 rounded">${service.base_url}</code></div>
                                        <div><strong>Status:</strong>
                                            <span class="px-2 py-1 text-xs rounded ${service.current_status.status === 'healthy' ? 'bg-green-100 text-green-800' :
                                                                                    service.current_status.status === 'degraded' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}">
                                                ${service.current_status.status}
                                            </span>
                                        </div>
                                        <div><strong>Response Time:</strong> ${service.current_status.response_time ? service.current_status.response_time.toFixed(3) + 's' : 'N/A'}</div>
                                        <div><strong>Last Check:</strong> ${service.current_status.last_check || 'N/A'}</div>
                                    </div>
                                </div>

                                <div>
                                    <h5 class="font-medium mb-2">Endpoints</h5>
                                    <div class="space-y-1 text-sm">
                                        <div><strong>Health:</strong> <code class="bg-gray-100 px-1 rounded">${service.endpoints.health}</code></div>
                                        <div><strong>Auth:</strong> <code class="bg-gray-100 px-1 rounded">${service.endpoints.auth}</code></div>
                                        <div><strong>OpenAPI:</strong> <code class="bg-gray-100 px-1 rounded">${service.endpoints.openapi}</code></div>
                                    </div>

                                    ${service.current_status.authentication_status ? `
                                    <div class="mt-3">
                                        <h5 class="font-medium mb-2">Authentication</h5>
                                        <span class="px-2 py-1 text-xs rounded ${service.current_status.authentication_status === 'authenticated' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                            ${service.current_status.authentication_status}
                                        </span>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>

                            ${service.current_status.error_message ? `
                            <div class="mt-4">
                                <h5 class="font-medium mb-2">Error Details</h5>
                                <div class="bg-red-50 border border-red-200 rounded p-3 text-sm text-red-800">
                                    ${service.current_status.error_message}
                                </div>
                            </div>
                            ` : ''}

                            ${service.current_status.details ? `
                            <div class="mt-4">
                                <h5 class="font-medium mb-2">Additional Details</h5>
                                ${service.current_status.details.servers && service.current_status.details.servers.length > 0 ? `
                                <div class="mb-3">
                                    <h6 class="font-medium text-sm mb-2">Server Status:</h6>
                                    ${service.current_status.details.servers.map(server => `
                                        <div class="bg-${server.status === 'error' || server.status === 'critical' ? 'red' : server.status === 'warning' || server.status === 'degraded' ? 'yellow' : 'green'}-50 border border-${server.status === 'error' || server.status === 'critical' ? 'red' : server.status === 'warning' || server.status === 'degraded' ? 'yellow' : 'green'}-200 rounded p-2 mb-2">
                                            <div class="text-sm">
                                                <strong>${server.name}</strong> (ID: ${server.id})
                                                <span class="ml-2 px-2 py-1 text-xs rounded-full bg-${server.status === 'error' || server.status === 'critical' ? 'red' : server.status === 'warning' || server.status === 'degraded' ? 'yellow' : 'green'}-100 text-${server.status === 'error' || server.status === 'critical' ? 'red' : server.status === 'warning' || server.status === 'degraded' ? 'yellow' : 'green'}-800">
                                                    ${server.status}
                                                </span>
                                            </div>
                                            ${server.issues && server.issues.length > 0 ? `
                                                <div class="mt-1 text-xs text-${server.status === 'error' || server.status === 'critical' ? 'red' : server.status === 'warning' || server.status === 'degraded' ? 'yellow' : 'green'}-700">
                                                    <strong>Issues:</strong> ${server.issues.join(', ')}
                                                </div>
                                            ` : ''}
                                            ${server.recommendations && server.recommendations.length > 0 ? `
                                                <div class="mt-1 text-xs text-blue-700">
                                                    <strong>Recommendations:</strong> ${server.recommendations.join(', ')}
                                                </div>
                                            ` : ''}
                                            ${server.response_time ? `
                                                <div class="mt-1 text-xs text-gray-600">
                                                    Response Time: ${server.response_time}ms
                                                </div>
                                            ` : ''}
                                            <div class="mt-1 text-xs text-gray-600">
                                                Last Check: ${server.last_check || 'N/A'}
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                                ` : ''}
                                <div class="bg-gray-100 p-3 rounded text-xs overflow-auto">
                                    <strong>Raw Details:</strong>
                                    <pre><code>${JSON.stringify(service.current_status.details, null, 2)}</code></pre>
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    `;

                    // Create and show modal
                    showModal('Service Details', detailsHtml);
                } else {
                    alert('Error: ' + response.error);
                }
        } catch (error) {
            console.error('Error:', error);
            alert('Failed to load service details');
        }
    }
    
    function showModal(title, content) {
        // Create modal HTML
        const modalHtml = `
            <div id="serviceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">${title}</h3>
                            <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                        <div class="mt-2">
                            ${content}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        $('body').append(modalHtml);

        // Close modal handlers
        $('#closeModal').click(function(e) {
            e.preventDefault();
            $('#serviceModal').remove();
        });

        // Close modal when clicking on backdrop
        $('#serviceModal').click(function(e) {
            if (e.target === this) {
                $('#serviceModal').remove();
            }
        });
    }
    
    // Add a refresh all widgets button if needed
    window.refreshAllWidgets = function() {
        if (window.dashboardLoader) {
            window.dashboardLoader.refreshAll();
        }
    };
});
</script>

{% endblock %}